package com.imile.attendance.report.month;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.calendar.UserCalendarService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.cycleConfig.CycleConfigManage;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.AttendanceDayTypeEnum;
import com.imile.attendance.enums.DimissionStatusEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.EntryStatusEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import com.imile.attendance.enums.vacation.PunchDayTypeEnum;
import com.imile.attendance.infrastructure.repository.abnormal.dto.AttendanceDetailDTO;
import com.imile.attendance.infrastructure.repository.abnormal.dto.LeaveHoursRecordDTO;
import com.imile.attendance.infrastructure.repository.abnormal.dto.UserAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.dto.UserMonthAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.dto.UserYearAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AttendanceEmployeeDetailQuery;
import com.imile.attendance.infrastructure.repository.calendar.dao.BaseDayInfoDao;
import com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.query.BaseDayQuery;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserBaseInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportAbnormalDao;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportDao;
import com.imile.attendance.infrastructure.repository.report.dto.UserMonthReportBaseDTO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportAbnormalDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportFormDO;
import com.imile.attendance.infrastructure.repository.report.query.MonthReportListQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.UserDayConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.loader.StrategyLoader;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import com.imile.attendance.report.AttendanceDayReportManage;
import com.imile.attendance.report.AttendanceReportCalcService;
import com.imile.attendance.report.AttendanceReportQueryService;
import com.imile.attendance.report.day.dto.UserDayReportAggregationDTO;
import com.imile.attendance.report.dto.AttendanceReportCalcContext;
import com.imile.attendance.report.mapstruct.AttendanceReportMapstruct;
import com.imile.attendance.report.mapstruct.UserMonthReportMapstruct;
import com.imile.attendance.report.month.builder.UserMonthReportBuilder;
import com.imile.attendance.report.month.calculator.AttendanceMetricsCalculator;
import com.imile.attendance.report.month.dto.AttendanceCycleDTO;
import com.imile.attendance.infrastructure.repository.report.dto.CountryAttendanceCycleDTO;
import com.imile.attendance.report.month.dto.UserReportAggregationDTO;
import com.imile.attendance.report.month.dto.UserMonthAbnormalStatisticsDTO;
import com.imile.attendance.report.month.dto.UserMonthReportMetricsDTO;
import com.imile.attendance.report.month.vo.UserMonthReportListVO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.user.UserService;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.common.page.PaginationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户月报服务
 *
 * <AUTHOR>
 * @menu 考勤月报
 * @date 2025/6/14
 */
@Service
@Slf4j
public class AttendanceMonthReportService {
    @Resource
    private UserService userService;
    @Resource
    private AttendanceReportQueryService attendanceReportQueryService;
    @Resource
    private BaseDayInfoDao baseDayInfoDao;
    @Resource
    private AttendancePermissionService attendancePermissionService;
    @Resource
    private AttendanceDayReportDao attendanceDayReportDao;
    @Resource
    private CycleConfigManage cycleConfigManage;
    @Resource
    private AttendanceUserService attendanceUserService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private UserEntryRecordDao userEntryRecordDao;
    @Resource
    private UserDimissionRecordDao userDimissionRecordDao;
    @Resource
    private AttendanceDayReportManage dayReportManage;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private AttendanceMetricsCalculator attendanceMetricsCalculator;
    @Resource
    private UserMonthReportBuilder userMonthReportBuilder;
    @Resource
    private AttendanceDayReportAbnormalDao attendanceDayReportAbnormalDao;
    @Resource
    private MigrationService migrationService;
    @Resource
    private UserCalendarService userCalendarService;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private EmployeeAbnormalAttendanceService abnormalAttendanceService;


    private static final Integer DECEMBER = 12;
    private static final Integer JANUARY = 1;

    /**
     * 月报列表
     *
     * @param query 查询条件
     * @return List<UserMonthReportListVO>
     */
    public PaginationResult<UserMonthReportListVO> list(MonthReportListQuery query) {
        PaginationResult<UserMonthReportListVO> paginationResult = buildUserMonthReportQuery(query);
        if (paginationResult != null) {
            return paginationResult;
        }
        PageInfo<UserMonthReportBaseDTO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount()).doSelectPageInfo(
                () -> attendanceDayReportDao.page(query)
        );
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<UserMonthReportBaseDTO> resultList = pageInfo.getList();
        List<UserMonthReportListVO> reportListVOList = buildUserMonthReportResult(query, resultList);
        return PageUtil.getPageResult(reportListVOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 批量查询月报
     *
     * @param query
     * @return
     */
    public List<UserMonthReportListVO> selectList(Long startDayId, Long endDayId,
                                                  MonthReportListQuery query) {
        List<UserMonthReportBaseDTO> dtoList = attendanceDayReportDao.page(query);
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        List<UserMonthReportListVO> voList = UserMonthReportMapstruct.INSTANCE.toMonthReportListVO(dtoList);
        for (UserMonthReportListVO userMonthReportListVO : voList) {
            userMonthReportListVO.setAttendanceStartCycle(startDayId);
            userMonthReportListVO.setAttendanceEndCycle(endDayId);
        }
        List<Long> dayIdList = DateHelper.getDayIdList(startDayId, endDayId);
        Set<Long> dayIdSet = dayIdList.stream().collect(Collectors.toSet());
        Set<Long> userIdSet = voList
                .stream()
                .map(UserMonthReportListVO::getUserId)
                .collect(Collectors.toSet());
        this.buildMonthReportVO(voList, dtoList, userIdSet, dayIdSet, true, query.getDateTime());
        return voList;
    }

    /**
     * 构建用户周期内月报结果
     *
     * @param query      查询条件
     * @param resultList 查询结果
     * @return List<UserMonthReportListVO>
     */
    private List<UserMonthReportListVO> buildUserMonthReportResult(MonthReportListQuery query,
                                                                   List<UserMonthReportBaseDTO> resultList) {
        List<UserMonthReportListVO> monthReportListVOList = UserMonthReportMapstruct.INSTANCE.toMonthReportListVO(resultList);
        //获取当前人员的考勤周期
        Map<String, CountryAttendanceCycleDTO> cycleMap = query.getCycleMap();

        Set<Long> userIdSet = new HashSet<>();
        Set<Long> dayIdSet = new HashSet<>();

        for (UserMonthReportListVO userMonthReportListVO : monthReportListVOList) {
            Long userId = userMonthReportListVO.getUserId();

            String locationCountry = userMonthReportListVO.getLocationCountry();
            CountryAttendanceCycleDTO countryAttendanceCycleDTO = cycleMap.get(locationCountry);
            if (countryAttendanceCycleDTO == null) {
                log.warn("考勤周期配置不存在，跳过计算。用户ID: {}, 国家: {}", userId, locationCountry);
                continue;
            }
            userMonthReportListVO.setAttendanceStartCycle(countryAttendanceCycleDTO.getAttendanceStartCycle());
            userMonthReportListVO.setAttendanceEndCycle(countryAttendanceCycleDTO.getAttendanceEndCycle());
            userMonthReportListVO.setCycleStart(countryAttendanceCycleDTO.getCycleStart());
            userMonthReportListVO.setCycleEnd(countryAttendanceCycleDTO.getCycleEnd());
            userMonthReportListVO.setAttendanceMonth(query.getAttendanceMonth());
            userMonthReportListVO.setAttendanceCycle(AttendanceCycleDTO.formatAttendanceCycle(
                    countryAttendanceCycleDTO.getAttendanceStartCycle(),
                    countryAttendanceCycleDTO.getAttendanceEndCycle()
            ));

            userIdSet.add(userId);

            List<Long> dayIdList = DateHelper.getDayIdList(countryAttendanceCycleDTO.getAttendanceStartCycle(), countryAttendanceCycleDTO.getAttendanceEndCycle());
            dayIdSet.addAll(dayIdList);
        }
        this.buildMonthReportVO(monthReportListVOList, resultList, userIdSet, dayIdSet,
                query.getAreExport(), query.getDateTime());
        return monthReportListVOList;
    }

    /**
     * 查询时间范围内月报数据
     *
     * @param monthReportListVOList
     * @param resultList
     * @param userIdSet
     * @param dayIdSet
     * @param isExport
     */
    public void buildMonthReportVO(List<UserMonthReportListVO> monthReportListVOList,
                                   List<UserMonthReportBaseDTO> resultList,
                                   Set<Long> userIdSet,
                                   Set<Long> dayIdSet,
                                   Boolean isExport,
                                   Date dateTime) {
        // 获取用户ID列表
        List<Long> userIdList = resultList.stream()
                .map(UserMonthReportBaseDTO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        // 获取用户信息map <userId, AttendanceUser>
        Map<Long, AttendanceUser> userMap = attendanceUserService.listUsersByIds(userIdList)
                .stream()
                .collect(Collectors.toMap(AttendanceUser::getId, Function.identity(), (a, b) -> a));

        // 获取部门信息map <deptId, AttendanceDept>
        Map<Long, AttendanceDept> deptMap = deptService.listAllDeptInfo().stream()
                .collect(Collectors.toMap(AttendanceDept::getId, Function.identity(), (a, b) -> a));

        // 获取岗位信息map <postId, AttendancePost>
        Map<Long, AttendancePost> postMap = postService.listAllPostInfo().stream()
                .collect(Collectors.toMap(AttendancePost::getId, Function.identity(), (a, b) -> a));

        //查询员工入职记录map <userId, UserEntryRecordDO>
        Map<Long, UserEntryRecordDO> userEntryRecordMap = userEntryRecordDao.listByUserIds(userIdList).stream()
                .filter(item -> Objects.equals(EntryStatusEnum.ENTRY.getCode(), item.getEntryStatus()))
                .collect(Collectors.toMap(UserEntryRecordDO::getUserId, Function.identity(), (a, b) -> a));

        //查询员工离职记录map <userId, UserEntryRecordDO>
        Map<Long, UserDimissionRecordDO> userDimissionRecordDOMap = userDimissionRecordDao.listByUserIds(userIdList).stream()
                .filter(item -> Objects.equals(DimissionStatusEnum.DIMISSION.getCode(), item.getDimissionStatus()))
                .collect(Collectors.toMap(UserDimissionRecordDO::getUserId, Function.identity(), (a, b) -> a));

        //查询员工周期内的补卡次数
        Map<Long, List<UserCycleReissueCardCountDO>> userReissueCardCountMap = cycleConfigManage.selectByUserIdList(userIdList).stream()
                .collect(Collectors.groupingBy(UserCycleReissueCardCountDO::getUserId));

        //查询员工指定日期的排班
        Map<Long, List<UserShiftConfigDO>> userShiftConfigMap = userShiftConfigManage.selectBatchUserRecord(userIdList, new ArrayList<>(dayIdSet))
                .stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

        List<Long> allUserIds = new ArrayList<>(userIdSet);
        List<Long> allDayIds = new ArrayList<>(dayIdSet).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<AttendanceDayReportDO> dayReportDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allDayIds)) {
            //查询日报
            dayReportDOList = dayReportManage.listDayReportByUserIdsAndDayIds(
                    allUserIds, allDayIds);
        }

        Map<Long, List<AttendanceDayReportDO>> dayReportMap = dayReportDOList.stream()
                .collect(Collectors.groupingBy(AttendanceDayReportDO::getUserId));

        List<Long> dayReportIds = dayReportDOList.stream()
                .map(AttendanceDayReportDO::getId)
                .collect(Collectors.toList());

        // 查询日报单据信息<dayReportId, List<AttendanceDayReportFormDO>>
        Map<Long, List<AttendanceDayReportFormDO>> dayReportFormMap = dayReportManage.selectFormByReportIds(dayReportIds)
                .stream()
                .collect(Collectors.groupingBy(AttendanceDayReportFormDO::getDayReportId));

        // 查询用户的考勤规则信息
        List<Long> punchConfigIds = dayReportDOList.stream()
                .map(AttendanceDayReportDO::getPunchConfigId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigManage.getPunchConfigByIds(punchConfigIds)
                .stream()
                .collect(Collectors.toMap(PunchConfigDO::getId, Function.identity(), (a, b) -> a));

        // 查询用户的班次信息
        List<Long> punchClassConfigIds = dayReportDOList.stream()
                .map(AttendanceDayReportDO::getPunchClassConfigId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, PunchClassConfigDO> punchClassConfigMap = punchClassConfigManage.selectByClassIds(punchClassConfigIds)
                .stream()
                .collect(Collectors.toMap(PunchClassConfigDO::getId, Function.identity(), (a, b) -> a));
        Map<Long, List<PunchClassItemConfigDO>> punchClassItemConfigMap = punchClassConfigManage.selectClassItemByClassIds(punchClassConfigIds)
                .stream().collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));

        for (UserMonthReportListVO userMonthReportListVO : monthReportListVOList) {
            Long userId = userMonthReportListVO.getUserId();
            // 用户的日报信息
            List<AttendanceDayReportDO> userDayReportDOList = dayReportMap.getOrDefault(userId, Collections.emptyList());

            // 构建用户基本信息
            userMonthReportBuilder.buildUserBasicInfo(userMonthReportListVO, userDayReportDOList,
                    userMap, deptMap, postMap, userEntryRecordMap, userDimissionRecordDOMap);

            // 计算考勤指标
            UserMonthReportMetricsDTO metrics = attendanceMetricsCalculator.calculateMonthlyMetrics(
                    userId,
                    userMonthReportListVO.getAttendanceStartCycle(),
                    userMonthReportListVO.getAttendanceEndCycle(),
                    userDayReportDOList,
                    punchConfigMap,
                    punchClassConfigMap,
                    dayReportFormMap,
                    userReissueCardCountMap,
                    userShiftConfigMap);
            userMonthReportBuilder.buildAttendanceMetrics(userMonthReportListVO, metrics);

            // 导出使用：构建用户每日考勤结果
            if (isExport) {
                userMonthReportBuilder.buildDayAttendanceResult(
                        userMonthReportListVO, userDayReportDOList, metrics.getUserDayMetricsDTOList());
            }
        }

        // 构建异常统计数据
        buildAbnormalStatistics(monthReportListVOList, dayReportDOList,
                punchClassConfigMap, punchClassItemConfigMap, dateTime);
    }

    /**
     * 获取考勤周期
     *
     * @param cycleStart      考勤开始标记
     * @param cycleEnd        考勤结束标记
     * @param attendanceYear  考勤年
     * @param attendanceMonth 考勤月
     * @return AttendanceCycleDTO
     */
    public AttendanceCycleDTO getAttendanceCycleByYearAndMonth(String cycleStart,
                                                               String cycleEnd,
                                                               String attendanceYear,
                                                               String attendanceMonth) {
        if (cycleStart == null || cycleEnd == null) {
            return new AttendanceCycleDTO();
        }
        Date cycleStartDate = null;
        Date cycleEndDate = null;

        String cycleMonth = attendanceYear + attendanceMonth;
        if (StringUtils.equals(cycleEnd, CycleTypeEnum.END_OF_MONTH_CODE)) {
            cycleStartDate = DateUtil.beginOfMonth(DateUtil.parse(cycleMonth, "yyyyMM"));
            cycleEndDate = DateUtil.endOfMonth(DateUtil.parse(cycleMonth, "yyyyMM"));
        } else {
            cycleStartDate = DateUtil.offsetMonth(DateUtil.parse(cycleMonth + cycleStart, "yyyyMMdd"), -1);
            cycleEndDate = DateUtil.parse(DateUtil.format(DateUtil.offsetMonth(cycleStartDate, 1), "yyyyMM") + cycleEnd, "yyyyMMdd");
        }
        Long cycleStartDayId = DateHelper.getDayId(cycleStartDate);
        Long cycleEndDayId = DateHelper.getDayId(cycleEndDate);
        return AttendanceCycleDTO.of(cycleStartDayId, cycleEndDayId);
    }

    /**
     * 构建用户月报查询参数
     *
     * @param query 查询条件
     * @return PaginationResult<UserMonthReportListVO>
     */
    private PaginationResult<UserMonthReportListVO> buildUserMonthReportQuery(MonthReportListQuery query) {
        // 月份处理
        if (query.getAttendanceMonth() != null) {
            query.setAttendanceMonth(DateHelper.formatMonth(query.getAttendanceMonth()));
        }

        // 默认员工类型查询
        if (CollectionUtils.isEmpty(query.getEmployeeTypeList())) {
            query.setEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
        }

        // 封装仓内国家查询条件
        List<String> wareHouseCountry = migrationService.getWareHouseCountry();
        if (CollectionUtils.isNotEmpty(wareHouseCountry)) {
            query.setWareHouseCountry(wareHouseCountry);
        }
        // 封装考勤管理劳务派遣范围国家查询条件
        List<String> osCountry = migrationService.getAttendanceOSCountry();
        if (CollectionUtils.isNotEmpty(osCountry)) {
            query.setOsCountry(osCountry);
        }
        // 查询常驻国及部门权限
        List<String> authLocationCountryList = attendancePermissionService.getUserLocationCountryPermission();
        List<Long> authDeptIdList = attendancePermissionService.getUserDeptPermission();

        if ((CollectionUtils.isEmpty(authLocationCountryList)
                && CollectionUtils.isEmpty(authDeptIdList))) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        query.setAuthLocationCountryList(authLocationCountryList);
        query.setAuthDeptIdList(authDeptIdList);

        List<String> countries = cycleConfigManage.getAllEnabled().stream()
                .map(AttendanceCycleConfigDO::getCountry)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(countries)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<AttendanceCycleConfigDO> cycleConfigDOList = cycleConfigManage.selectByCountiesAndCycleType(
                countries, AttendanceCycleTypeEnum.MONTH.getType());
        Map<String, AttendanceCycleConfigDO> cycleConfigDOMap = cycleConfigDOList.stream()
                .collect(Collectors.toMap(AttendanceCycleConfigDO::getCountry, item -> item, (oldVar, newVar) -> oldVar));

        List<CountryAttendanceCycleDTO> cycleDTOList = new ArrayList<>();
        for (Map.Entry<String, AttendanceCycleConfigDO> entry : cycleConfigDOMap.entrySet()) {
            String country = entry.getKey();
            AttendanceCycleConfigDO attendanceCycleConfigDO = entry.getValue();
            AttendanceCycleDTO attendanceCycleDTO = this.getAttendanceCycleByYearAndMonth(
                    attendanceCycleConfigDO.getCycleStart(),
                    attendanceCycleConfigDO.getCycleEnd(),
                    query.getAttendanceYear(),
                    query.getAttendanceMonth());

            CountryAttendanceCycleDTO cycleDTO = CountryAttendanceCycleDTO.builder()
                    .country(country)
                    .cycleStartDate(attendanceCycleDTO.attendanceStartCycleDateStr())
                    .cycleEndDate(attendanceCycleDTO.attendanceEndCycleDateStr())
                    .attendanceStartCycle(attendanceCycleDTO.getAttendanceStartCycle())
                    .attendanceEndCycle(attendanceCycleDTO.getAttendanceEndCycle())
                    .cycleStart(attendanceCycleConfigDO.getCycleStart())
                    .cycleEnd(attendanceCycleConfigDO.getCycleEnd())
                    .build();
            cycleDTOList.add(cycleDTO);
        }
        // 转成 Map<country, CountryAttendanceCycleDTO>
        Map<String, CountryAttendanceCycleDTO> cycleMap = cycleDTOList.stream()
                .collect(Collectors.toMap(CountryAttendanceCycleDTO::getCountry, Function.identity()));
        query.setCycleMap(cycleMap);
        return null;
    }

    /**
     * 月报详情
     *
     * @param query
     * @return
     */
    public UserAttendanceDTO detail(AttendanceEmployeeDetailQuery query) {
        // 1.查询员工基本信息
        UserBaseInfoDTO userBaseInfo = userService.getUserBaseInfo(query.getUserCode());

        // 2.查询员工考勤明细
        UserReportAggregationDTO userReportDTO = attendanceReportQueryService.getUserReportDTO(userBaseInfo, query.getYear(), query.getMonth());

        // 3.查询时间范围内日期
        UserAttendanceDTO userAttendanceDTO = AttendanceReportMapstruct.INSTANCE.toUserAttendanceDTO(userBaseInfo);

        // 4.查询时间范围内日期
        BaseDayQuery baseDayQuery = BaseDayQuery.builder().year(query.getYear()).month(query.getMonth()).build();
        Map<Integer, List<BaseDayInfoDO>> dayMap = baseDayInfoDao.getBaseDay(baseDayQuery)
                .stream().collect(Collectors.groupingBy(BaseDayInfoDO::getMonth));

        // 5. 封装明细考勤记录(年/月)
        UserYearAttendanceDTO userYearAttendanceDTO = new UserYearAttendanceDTO();
        userYearAttendanceDTO.setYear(query.getYear());
        List<UserMonthAttendanceDTO> userMonthAttendances = new ArrayList<>();
        userYearAttendanceDTO.setUserMonthAttendances(userMonthAttendances);
        for (int month = JANUARY; month <= DECEMBER; month++) {
            List<BaseDayInfoDO> baseDays = dayMap.get(month);
            if (CollectionUtils.isEmpty(baseDays)) {
                continue;
            }
            UserMonthAttendanceDTO userMonthAttendanceDTO = new UserMonthAttendanceDTO();
            userMonthAttendanceDTO.setMonth((long) month);
            List<AttendanceDetailDTO> res = new ArrayList<>();
            userMonthAttendanceDTO.setAttendances(res);
            userMonthAttendances.add(userMonthAttendanceDTO);
            //按天遍历
            this.dayCalcHandler(baseDays, res, userReportDTO);
        }
        //用户考勤记录
        userAttendanceDTO.setUserYearAttendance(userYearAttendanceDTO);
        userAttendanceDTO.setAttendanceConfigType(userReportDTO.getCalendarConfig().getType());
        return userAttendanceDTO;
    }

    private void dayCalcHandler(List<BaseDayInfoDO> baseDays,
                                List<AttendanceDetailDTO> res,
                                UserReportAggregationDTO userReportDTO) {
        Long dayIdNow = Long.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
        // 查询整年日历 (不能根据正常考勤表遍历，需要根据整年的日历遍历，一天没有正常，全是异常，也是可以计算出来时间的)
        Map<Long, CalendarConfigDetailDO> configDetailMap = userReportDTO.getCalendarConfigDetailList()
                .stream()
                .collect(Collectors.toMap(CalendarConfigDetailDO::getDayId, item -> item, (oldVar, newVar) -> oldVar));
        // 封装当日出勤信息
        for (BaseDayInfoDO baseDay : baseDays) {
            Long dayId = baseDay.getId();
            AttendanceDetailDTO attendanceDetailDTO = new AttendanceDetailDTO();
            res.add(attendanceDetailDTO);
            attendanceDetailDTO.setDate(DateUtil.beginOfDay(DateUtil.parse(dayId.toString(), "yyyyMMdd")));
            attendanceDetailDTO.setDay(DateUtil.dayOfMonth(attendanceDetailDTO.getDate()));
            attendanceDetailDTO.setDayId(dayId);
            attendanceDetailDTO.setAttendanceType(AttendanceDayTypeEnum.PRESENT.name());
            CalendarConfigDetailDO configDetailDO = configDetailMap.get(dayId);
            if (configDetailDO != null) {
                attendanceDetailDTO.setAttendanceType(configDetailDO.getDayType());
            }
            if (dayIdNow.compareTo(dayId) < 0) {
                continue;
            }
            //获取当天的正常考勤
            List<AttendanceEmployeeDetailDO> dayRecordList = userReportDTO.getAttendanceEmployeeDetailList()
                    .stream().filter(item -> item.getDayId().equals(dayId)).collect(Collectors.toList());
            List<AttendanceEmployeeDetailDO> isAttendanceList = dayRecordList
                    .stream().filter(record -> record.getIsAttendance() == 1).collect(Collectors.toList());
            attendanceDetailDTO.setIsAttendance(CollectionUtils.isEmpty(isAttendanceList) ? 0 : 1);
            List<AttendanceEmployeeDetailDO> isNeedAttendanceList = dayRecordList.stream()
                    .filter(record -> StringUtils.equalsIgnoreCase("PRESENT", record.getAttendanceType()))
                    .collect(Collectors.toList());
            attendanceDetailDTO.setIsNeedAttendance(CollectionUtils.isEmpty(isNeedAttendanceList) ? 0 : 1);

            BigDecimal overtimeMinutes = BigDecimal.ZERO;
            BigDecimal attendanceMinutes = BigDecimal.ZERO;
            BigDecimal oooMinutes = BigDecimal.ZERO;
            for (AttendanceEmployeeDetailDO detailDO : dayRecordList) {
                attendanceDetailDTO.setAttendanceConcreteType(detailDO.getConcreteType());
                if (detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                    attendanceMinutes = attendanceMinutes.add(detailDO.getAttendanceMinutes());
                    if (AttendanceConcreteTypeEnum.OOO.getCode().equals(detailDO.getConcreteType())) {
                        oooMinutes = oooMinutes.add(detailDO.getAttendanceMinutes());
                    }
                }
                if (detailDO.getOvertimeMinutes() != null && detailDO.getOvertimeMinutes().compareTo(BigDecimal.ZERO) > 0) {
                    overtimeMinutes = overtimeMinutes.add(detailDO.getOvertimeMinutes());
                }
            }
            attendanceDetailDTO.setOvertimeMinutes(overtimeMinutes);
            attendanceDetailDTO.setAttendanceMinutes(attendanceMinutes);
            attendanceDetailDTO.setOooMinutes(oooMinutes);
            attendanceDetailDTO.setOvertimeHours(overtimeMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
            attendanceDetailDTO.setAttendanceHours(attendanceMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));

            //根据当天的出勤类型进行分组(请假)
            List<LeaveHoursRecordDTO> leaveHoursRecordDTOList = new ArrayList<>();
            Map<String, List<AttendanceEmployeeDetailDO>> dayLeaveGroup = dayRecordList.stream().filter(item -> StringUtils.isNotBlank(item.getConcreteType()) && item.getLeaveMinutes() != null && item.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.groupingBy(AttendanceEmployeeDetailDO::getConcreteType));
            BigDecimal totalLeaveMinutes = BigDecimal.ZERO;
            for (Map.Entry<String, List<AttendanceEmployeeDetailDO>> leaveEntry : dayLeaveGroup.entrySet()) {
                List<AttendanceEmployeeDetailDO> leaveList = leaveEntry.getValue();
                BigDecimal leaveMinutes = BigDecimal.ZERO;
                List<String> picturePathList = new ArrayList<>();
                for (AttendanceEmployeeDetailDO leaveDetailDO : leaveList) {
                    leaveMinutes = leaveMinutes.add(leaveDetailDO.getLeaveMinutes());
                    List<AttendanceFormAttrDO> attachmentDOList = userReportDTO.getFormAttrList().stream()
                            .filter(item -> item.getFormId().equals(leaveDetailDO.getFormId())
                                    && StringUtils.equalsIgnoreCase(item.getAttrKey(),
                                    ApplicationFormAttrKeyEnum.attachmentList.getLowerCode()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(attachmentDOList)) {
                        List<AttachmentDTO> attachmentDTOS = JSONObject.parseArray(attachmentDOList.get(0).getAttrValue(), AttachmentDTO.class);
                        for (AttachmentDTO attachmentDTO : attachmentDTOS) {
                            picturePathList.add(attachmentDTO.getUrlPath());
                        }
                    }
                }
                LeaveHoursRecordDTO leaveHoursRecordDTO = getLeaveHoursRecordDTO(leaveList, leaveMinutes, picturePathList);
                leaveHoursRecordDTOList.add(leaveHoursRecordDTO);
                attendanceDetailDTO.setAttendanceConcreteType(leaveList.get(0).getConcreteType());
                totalLeaveMinutes = totalLeaveMinutes.add(leaveMinutes);
            }
            attendanceDetailDTO.setLeaveHoursRecordDTOList(leaveHoursRecordDTOList);
            attendanceDetailDTO.setLeaveMinutes(totalLeaveMinutes);
            attendanceDetailDTO.setLeaveHours(totalLeaveMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
            //查询当天是否包含考勤流程
            attendanceDetailDTO.setHasSubmittedAttendanceProcess(this.hasDayForm(dayId
                    , userReportDTO.getPassAndInViewFormList()
                    , userReportDTO.getPassAndInViewFormAttrList()));

            //获取当天所有异常
            List<EmployeeAbnormalAttendanceDO> dayAbnormalAttendanceList = userReportDTO.getAbnormalAttendanceList()
                    .stream().filter(item -> item.getDayId().compareTo(dayId) == 0)
                    .collect(Collectors.toList());

            //获取当天的排班和打卡班次信息
            List<UserShiftConfigDO> dayShiftList = userReportDTO.getUserShiftConfigList().stream()
                    .filter(item -> item.getDayId().compareTo(dayId) == 0)
                    .collect(Collectors.toList());
            List<PunchClassConfigDO> dayClassConfigList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dayShiftList) && dayShiftList.get(0).getPunchClassConfigId() != null) {
                dayClassConfigList = userReportDTO.getClassConfigList().stream()
                        .filter(item -> item.getId().equals(dayShiftList.get(0).getPunchClassConfigId()))
                        .collect(Collectors.toList());
            }
            List<PunchClassItemConfigDO> dayClassItemConfigDOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dayClassConfigList)) {
                PunchClassConfigDO classConfigDO = dayClassConfigList.get(0);
                dayClassItemConfigDOList = userReportDTO.getClassItemConfigList().stream()
                        .filter(item -> item.getPunchClassId().equals(classConfigDO.getId()))
                        .collect(Collectors.toList());
            }
            // 获取当天打卡规则
            List<UserDayConfigDTO> userDayConfigList = userReportDTO.getUserDayConfigDTOList().stream()
                    .filter(item -> dayId.equals(item.getDayId()))
                    .collect(Collectors.toList());
            PunchConfigDO punchConfigDO = CollectionUtils.isEmpty(userDayConfigList)
                    ? null : userDayConfigList.get(0).getPunchConfigDO();
            //注意特殊情况，免打卡
            if (Objects.nonNull(punchConfigDO)
                    && PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode().equals(punchConfigDO.getConfigType())) {
                if (attendanceDetailDTO.getLeaveMinutes() == null
                        || attendanceDetailDTO.getLeaveMinutes().compareTo(BigDecimal.ZERO) == 0) {
                    attendanceDetailDTO.setAttendanceConcreteType("P");
                    if (CollectionUtils.isNotEmpty(dayShiftList)
                            && DayShiftRuleEnum.isRestDay(dayShiftList.get(0).getDayShiftRule())) {
                        attendanceDetailDTO.setAttendanceConcreteType(dayShiftList.get(0).getDayShiftRule());
                    }
                    continue;
                }
                if (attendanceDetailDTO.getLeaveMinutes()
                        .compareTo(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES)) == 0) {
                    attendanceDetailDTO.setAttendanceConcreteType("L");
                    continue;
                }
                attendanceDetailDTO.setAttendanceMinutes(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS
                        .multiply(BusinessConstant.MINUTES)
                        .subtract(attendanceDetailDTO.getLeaveMinutes()));
                attendanceDetailDTO.setAttendanceHours(attendanceDetailDTO.getAttendanceMinutes()
                        .divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
                continue;
            }
            UserDayReportAggregationDTO userDayReportAggregationDTO = UserDayReportAggregationDTO.builder()
                    .dayRecordList(dayRecordList)
                    .dayAbnormalAttendanceList(dayAbnormalAttendanceList)
                    .punchConfig(punchConfigDO)
                    .dayShift(CollectionUtils.isEmpty(dayShiftList)
                            ? null : dayShiftList.get(0))
                    .dayClassConfigList(dayClassConfigList)
                    .dayClassItemConfigDOList(dayClassItemConfigDOList)
                    .build();
            // 当日异常类型计算
            this.dayTypeHandler(attendanceDetailDTO, userDayReportAggregationDTO
                    , userReportDTO.getUserPunchRecordList()
                    , userReportDTO.getPassAndInViewFormList()
                    , userReportDTO.getPassAndInViewFormAttrList());
        }
    }

    private void dayTypeHandler(AttendanceDetailDTO attendanceDetailDTO,
                                UserDayReportAggregationDTO userDayReportAggregationDTO,
                                List<UserPunchRecordDTO> userPunchRecordList,
                                List<AttendanceFormDO> passAndInViewFormList,
                                List<AttendanceFormAttrDO> passAndInViewFormAttrList) {
        attendanceDetailDTO.setDefaultLegalWorkingHours(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
        List<BigDecimal> defaultAbnormalHours = userDayReportAggregationDTO.getDayRecordList().stream()
                .filter(item -> item.getLegalWorkingHours() != null
                        && item.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0
                        && item.getAttendanceMinutes() != null
                        && item.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0)
                .map(AttendanceEmployeeDetailDO::getLegalWorkingHours).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(defaultAbnormalHours)) {
            attendanceDetailDTO.setDefaultLegalWorkingHours(defaultAbnormalHours.get(0));
        }
        List<PunchClassConfigDO> dayClassConfigList = userDayReportAggregationDTO.getDayClassConfigList();
        if (CollectionUtils.isNotEmpty(dayClassConfigList)) {
            if (dayClassConfigList.get(0).getLegalWorkingHours() != null && dayClassConfigList.get(0).getLegalWorkingHours().compareTo(BigDecimal.ZERO) > -1) {
                attendanceDetailDTO.setDefaultLegalWorkingHours(dayClassConfigList.get(0).getLegalWorkingHours());
            }
        }
        PunchConfigDO punchConfig = userDayReportAggregationDTO.getPunchConfig();
        if (Objects.isNull(punchConfig)) {
            attendanceDetailDTO.setAttendanceConcreteType("A");
            return;
        }
        if (PunchConfigTypeEnum.isFlexibleWorkTwice(punchConfig.getConfigType())) {
            attendanceDetailDTO.setDefaultLegalWorkingHours(punchConfig.getPunchTimeInterval());
        }
        //先判断当天正常表时间够不够，正常就直接返回
        if (((attendanceDetailDTO.getAttendanceMinutes()
                .add(attendanceDetailDTO.getLeaveMinutes()))
                .compareTo(attendanceDetailDTO.getDefaultLegalWorkingHours().multiply(BusinessConstant.MINUTES)) > -1)
                && !PunchConfigTypeEnum.isFlexibleWorkTwice(punchConfig.getConfigType())) {
            return;
        }
        //后续的所有情况都是当天没有完整出勤

        //当天未排班，需要看一下正常出勤表有没有数据(有的话也是异常处理确认后的数据)
        UserShiftConfigDO dayShift = userDayReportAggregationDTO.getDayShift();
        if (Objects.isNull(dayShift)) {
            //当天未排班，也没有处理异常，就是A缺勤
            attendanceDetailDTO.setAttendanceConcreteType("A");
            return;
        }
        //当天排班如果是PH/OFF就无需调用
        if (PunchDayTypeEnum.H.getCode().equals(dayShift.getDayShiftRule())
                || PunchDayTypeEnum.OFF.getCode().equals(dayShift.getDayShiftRule())) {
            return;
        }
        //添加打卡规则类型
        attendanceDetailDTO.setPunchConfigType(punchConfig.getConfigType());
        attendanceDetailDTO.setPunchConfigTypeDesc(RequestInfoHolder.isChinese() ?
                PunchConfigTypeEnum.getInstance(punchConfig.getConfigType()).getDesc() :
                PunchConfigTypeEnum.getInstance(punchConfig.getConfigType()).getDescEn());
        // 获取请假/外勤时长
        BigDecimal formMinutes = getFormMinutes(userDayReportAggregationDTO);
        BigDecimal availableMinutes = BigDecimal.ZERO;

        //接下来需要重新计算当天的考勤，根据打卡时间来获取
        AttendanceReportCalcContext reportContext = AttendanceReportCalcContext.builder()
                .dayId(attendanceDetailDTO.getDayId())
                .dayClassItemConfigList(userDayReportAggregationDTO.getDayClassItemConfigDOList())
                .userPunchRecordList(userPunchRecordList)
                .attendanceMinutes(attendanceDetailDTO.getAttendanceMinutes())
                .leaveMinutes(attendanceDetailDTO.getLeaveMinutes())
                .formMinutes(formMinutes)
                .defaultMinutes(attendanceDetailDTO.getDefaultLegalWorkingHours().multiply(BusinessConstant.MINUTES))
                .passAndInViewFormList(passAndInViewFormList)
                .passAndInViewFormAttrList(passAndInViewFormAttrList)
                .build();
        AttendanceReportCalcService reportService = StrategyLoader.load(AttendanceReportCalcService.class,
                t -> t.isMatch(punchConfig.getConfigType()));
        if (Objects.nonNull(reportService)) {
            availableMinutes = reportService.execute(reportContext);
        }
        // 灵活打卡两次且满足时长适配
        if (((attendanceDetailDTO.getAttendanceMinutes()
                .add(attendanceDetailDTO.getLeaveMinutes()))
                .compareTo(attendanceDetailDTO.getDefaultLegalWorkingHours().multiply(BusinessConstant.MINUTES)) > -1)
                && PunchConfigTypeEnum.isFlexibleWorkTwice(punchConfig.getConfigType())) {
            //在减去单据申请时间
            availableMinutes = availableMinutes.compareTo(BigDecimal.ZERO) <= 0
                    ? BigDecimal.ZERO
                    : availableMinutes.subtract(attendanceDetailDTO.getLeaveMinutes());
            if (attendanceDetailDTO.getOooMinutes().compareTo(BigDecimal.ZERO) > 0) {
                availableMinutes = availableMinutes.add(attendanceDetailDTO.getOooMinutes());
            }
            attendanceDetailDTO.setAttendanceMinutes(availableMinutes);
            attendanceDetailDTO.setAttendanceHours(availableMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
            return;
        }

        if (availableMinutes.compareTo(BigDecimal.ZERO) == 0) {
            attendanceDetailDTO.setAttendanceConcreteType("A");
            return;
        }
        //当天存在出勤时间，可能是请假也可能是打卡，或者两者都有
        if (attendanceDetailDTO.getLeaveMinutes() == null || attendanceDetailDTO.getLeaveMinutes().compareTo(BigDecimal.ZERO) == 0) {
            //没有请假时间
            attendanceDetailDTO.setAttendanceConcreteType("P");
        }
        //在减去单据申请时间
        availableMinutes = availableMinutes.subtract(attendanceDetailDTO.getLeaveMinutes());
        attendanceDetailDTO.setAttendanceMinutes(availableMinutes);
        attendanceDetailDTO.setAttendanceHours(availableMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
        if (availableMinutes.compareTo(attendanceDetailDTO.getDefaultLegalWorkingHours().multiply(BusinessConstant.MINUTES)) < 0) {
            attendanceDetailDTO.setAttendanceConcreteType("A");
        }
    }

    @NotNull
    private static BigDecimal getFormMinutes(UserDayReportAggregationDTO userDayReportAggregationDTO) {
        BigDecimal formMinutes = BigDecimal.ZERO;
        for (AttendanceEmployeeDetailDO detailDO : userDayReportAggregationDTO.getDayRecordList()) {
            if (detailDO.getFormId() != null && detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                formMinutes = formMinutes.add(detailDO.getLeaveMinutes());
            }
            if (detailDO.getFormId() != null && detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                formMinutes = formMinutes.add(detailDO.getAttendanceMinutes());
            }
        }
        return formMinutes;
    }


    @NotNull
    private static LeaveHoursRecordDTO getLeaveHoursRecordDTO(List<AttendanceEmployeeDetailDO> leaveList, BigDecimal leaveMinutes, List<String> picturePathList) {
        LeaveHoursRecordDTO leaveHoursRecordDTO = new LeaveHoursRecordDTO();
        leaveHoursRecordDTO.setLeaveType(leaveList.get(0).getLeaveType());
        leaveHoursRecordDTO.setLeaveMinutes(leaveMinutes);
        leaveHoursRecordDTO.setLeaveHours(leaveMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
        leaveHoursRecordDTO.setLeaveConcreteType(leaveList.get(0).getConcreteType());
        leaveHoursRecordDTO.setPicturePathList(picturePathList);
        return leaveHoursRecordDTO;
    }

    /**
     * 查询请假周期包含当天的审批单据，并且没有被销假
     *
     * @param dayId
     * @param formDOList
     * @param formAttrList
     * @return
     */
    private Boolean hasDayForm(Long dayId,
                               List<AttendanceFormDO> formDOList,
                               List<AttendanceFormAttrDO> formAttrList) {
        //查询请假周期包含当天的审批单据，并且没有被销假
        if (CollectionUtils.isEmpty(formDOList) || CollectionUtils.isEmpty(formAttrList)) {
            return false;
        }
        Map<Long, List<AttendanceFormAttrDO>> passFormAttrMap = formAttrList.stream().collect(Collectors.groupingBy(o -> o.getFormId()));
        for (AttendanceFormDO formDO : formDOList) {
            List<AttendanceFormAttrDO> formAttrDOList = passFormAttrMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(formAttrDOList)) {
                continue;
            }
            List<AttendanceFormAttrDO> isRevokeDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.isRevoke.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(isRevokeDO) && StringUtils.isNotBlank(isRevokeDO.get(0).getAttrValue()) && isRevokeDO.get(0).getAttrValue().equals(BusinessConstant.Y.toString())) {
                continue;
            }
            List<AttendanceFormAttrDO> leaveStartDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
            List<AttendanceFormAttrDO> leaveEndDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
            List<AttendanceFormAttrDO> reissueCardDayIdDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode())).collect(Collectors.toList());
            List<AttendanceFormAttrDO> outOfOfficeStartDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
            List<AttendanceFormAttrDO> outOfOfficeEndDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
            //查询请假时间是不是包含本次周期
            if (CollectionUtils.isNotEmpty(leaveStartDateDO) && CollectionUtils.isNotEmpty(leaveEndDateDO)) {
                Long leaveStartDayId = Long.valueOf(DateUtil.format(DateUtil.parse(leaveStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"), "yyyyMMdd"));
                DateTime leaveEndDate = DateUtil.parse(leaveEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Long leaveEndDayId = Long.valueOf(DateUtil.format(leaveEndDate, "yyyyMMdd"));
                if (leaveEndDayId.compareTo(dayId) < 0 || leaveStartDayId.compareTo(dayId) > 0) {
                    continue;
                }
                //结束时间不能为当天的最早时间
                DateTime attendanceDate = DateUtil.parse(String.valueOf(dayId), "yyyyMMdd");
                DateTime beginOfDay = DateUtil.beginOfDay(attendanceDate);
                if (leaveEndDate.compareTo(beginOfDay) == 0) {
                    continue;
                }

                return true;
            }
            if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDO) && CollectionUtils.isNotEmpty(outOfOfficeEndDateDO)) {
                Long outOfOfficeStartDate = Long.valueOf(DateUtil.format(DateUtil.parse(outOfOfficeStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"), "yyyyMMdd"));
                Long outOfOfficeEndDate = Long.valueOf(DateUtil.format(DateUtil.parse(outOfOfficeEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"), "yyyyMMdd"));
                if (outOfOfficeEndDate.compareTo(dayId) < 0 || outOfOfficeStartDate.compareTo(dayId) > 0) {
                    continue;
                }

                return true;
            }
            if (CollectionUtils.isNotEmpty(reissueCardDayIdDO)) {
                Long reissueCardDayId = Long.valueOf(reissueCardDayIdDO.get(0).getAttrValue());
                if (dayId.equals(reissueCardDayId)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 构建异常统计数据
     * 为每个用户计算月度考勤周期内的异常情况统计
     *
     * @param monthReportListVOList 月报列表
     * @param dayReportDOList       日报数据列表
     */
    private void buildAbnormalStatistics(List<UserMonthReportListVO> monthReportListVOList,
                                         List<AttendanceDayReportDO> dayReportDOList,
                                         Map<Long, PunchClassConfigDO> punchClassConfigMap,
                                         Map<Long, List<PunchClassItemConfigDO>> punchClassItemConfigMap,
                                         Date dateTime) {
        if (CollectionUtils.isEmpty(monthReportListVOList) || CollectionUtils.isEmpty(dayReportDOList)) {
            return;
        }

        // 提取日报ID列表，用于查询异常数据
        List<Long> dayReportIds = dayReportDOList.stream()
                .map(AttendanceDayReportDO::getId)
                .collect(Collectors.toList());

        // 批量查询异常数据
        List<AttendanceDayReportAbnormalDO> abnormalDOList = attendanceDayReportAbnormalDao.selectByReportIds(dayReportIds);

        // 按用户ID分组日报数据，用于计算异常时间
        Map<Long, List<AttendanceDayReportDO>> userDayReportMap = dayReportDOList.stream()
                .collect(Collectors.groupingBy(AttendanceDayReportDO::getUserId));

        // 按日报ID分组异常数据，便于关联查询
        Map<Long, List<AttendanceDayReportAbnormalDO>> dayReportAbnormalMap = abnormalDOList.stream()
                .collect(Collectors.groupingBy(AttendanceDayReportAbnormalDO::getDayReportId));

        // 为每个用户构建异常统计
        for (UserMonthReportListVO userMonthReportListVO : monthReportListVOList) {
            Long userId = userMonthReportListVO.getUserId();
            List<AttendanceDayReportDO> userDayReports = userDayReportMap.getOrDefault(userId, Collections.emptyList());

            // 计算用户的异常统计数据
            List<UserMonthAbnormalStatisticsDTO> abnormalStatistics = calculateUserAbnormalStatistics(userDayReports,
                    dayReportAbnormalMap, punchClassConfigMap, punchClassItemConfigMap, dateTime);

            userMonthReportListVO.setAbnormalStatisticsList(abnormalStatistics);
        }
    }

    /**
     * 计算单个用户的异常统计数据
     * 包括异常次数统计和异常时间累计
     *
     * @param userDayReports       用户日报数据
     * @param dayReportAbnormalMap 日报异常数据映射
     * @return 异常统计列表
     */
    private List<UserMonthAbnormalStatisticsDTO> calculateUserAbnormalStatistics(
            List<AttendanceDayReportDO> userDayReports,
            Map<Long, List<AttendanceDayReportAbnormalDO>> dayReportAbnormalMap,
            Map<Long, PunchClassConfigDO> punchClassConfigMap,
            Map<Long, List<PunchClassItemConfigDO>> punchClassItemConfigMap,
            Date dateTime) {

        // 统计异常次数：按异常类型分组计数
        Map<String, Integer> abnormalCountMap = new HashMap<>();

        // 统计异常时间：累计特定类型的异常时间
        Map<String, BigDecimal> abnormalTimeMap = new HashMap<>();
        abnormalTimeMap.put(AttendanceAbnormalTypeEnum.LATE.getCode(), BigDecimal.ZERO);
        abnormalTimeMap.put(AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), BigDecimal.ZERO);
        abnormalTimeMap.put(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), BigDecimal.ZERO);

        // 遍历用户的日报数据
        for (AttendanceDayReportDO dayReport : userDayReports) {
            // 累计异常时间（从日报表中获取）
            if (dayReport.getLateMinutes() != null && dayReport.getLateMinutes().compareTo(BigDecimal.ZERO) > 0) {
                abnormalTimeMap.put(AttendanceAbnormalTypeEnum.LATE.getCode(),
                        abnormalTimeMap.get(AttendanceAbnormalTypeEnum.LATE.getCode()).add(dayReport.getLateMinutes()));
            }
            if (dayReport.getLeaveEarlyMinutes() != null && dayReport.getLeaveEarlyMinutes().compareTo(BigDecimal.ZERO) > 0) {
                abnormalTimeMap.put(AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(),
                        abnormalTimeMap.get(AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode()).add(dayReport.getLeaveEarlyMinutes()));
            }
            if (dayReport.getAbnormalWorkMinutes() != null && dayReport.getAbnormalWorkMinutes().compareTo(BigDecimal.ZERO) > 0) {
                abnormalTimeMap.put(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(),
                        abnormalTimeMap.get(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode()).add(dayReport.getAbnormalWorkMinutes()));
            }
            List<PunchClassItemConfigDO> classItemConfigDOList = new ArrayList<>();
            if (Objects.nonNull(dayReport.getPunchClassConfigId())) {
                PunchClassConfigDO punchClassConfigDO = punchClassConfigMap.get(dayReport.getPunchClassConfigId());
                if (Objects.nonNull(punchClassConfigDO)) {
                    classItemConfigDOList = punchClassItemConfigMap.get(punchClassConfigDO.getId());
                }
            }

            // 统计异常次数（从异常表中获取）
            List<AttendanceDayReportAbnormalDO> dayAbnormals = dayReportAbnormalMap.getOrDefault(dayReport.getId(), Collections.emptyList());
            Long dayId = DateHelper.getDayId(dateTime);
            if (Objects.nonNull(dayId)) {
                Long previousDayId = DateHelper.getPreviousDayId(dayId);
                Long nextDayId = DateHelper.getNextDayId(dayId);
                List<Long> dayIdList = Arrays.asList(dayId, previousDayId, nextDayId);
                // 过滤异常
                if (dayIdList.contains(dayReport.getDayId()) && CollectionUtils.isNotEmpty(classItemConfigDOList)) {
                    List<PunchClassItemConfigDO> classItemConfigList = PunchClassItemConfigMapstruct.INSTANCE.deepCopy(classItemConfigDOList);
                    List<EmployeeAbnormalAttendanceDO> abnormalDOList = AttendanceReportMapstruct.INSTANCE.toAbnormalDOByAbnormalReportDO(dayAbnormals);
                    punchClassConfigQueryService.transferItemConfigTimeFormat(classItemConfigList, dayReport.getDayId());
                    abnormalDOList = abnormalAttendanceService.filterAbnormalAttendance(abnormalDOList, classItemConfigList, dateTime);
                    if (CollectionUtils.isNotEmpty(abnormalDOList)) {
                        List<Long> abnormalIds = abnormalDOList.stream().map(EmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
                        dayAbnormals = dayAbnormals.stream()
                                .filter(item -> abnormalIds.contains(item.getAbnormalId()))
                                .collect(Collectors.toList());
                    }
                }
            }
            for (AttendanceDayReportAbnormalDO abnormal : dayAbnormals) {
                String abnormalType = abnormal.getAbnormalType();
                abnormalCountMap.put(abnormalType, abnormalCountMap.getOrDefault(abnormalType, 0) + 1);
            }
        }

        // 构建异常统计结果列表
        List<UserMonthAbnormalStatisticsDTO> result = new ArrayList<>();

        // 获取所有出现过的异常类型（只统计有异常次数的类型）
        for (Map.Entry<String, Integer> entry : abnormalCountMap.entrySet()) {
            String abnormalType = entry.getKey();
            Integer count = entry.getValue();

            // 跳过正常类型
            if (AttendanceAbnormalTypeEnum.NORMAL.getCode().equals(abnormalType)) {
                continue;
            }

            // 只显示有异常次数的类型
            if (count > 0) {
                // 获取异常类型描述
                String abnormalTypeDesc = AttendanceAbnormalTypeEnum.getAbnormalTypeDesc(abnormalType);
                // 获取异常时间（如果该类型支持时间统计）
                String abnormalTime = formatAbnormalTime(abnormalType, abnormalTimeMap.get(abnormalType));

                UserMonthAbnormalStatisticsDTO statistics = UserMonthAbnormalStatisticsDTO.builder()
                        .abnormalType(abnormalType)
                        .abnormalTypeDesc(abnormalTypeDesc)
                        .abnormalCount(count)
                        .abnormalTime(abnormalTime)
                        .build();

                result.add(statistics);
            }
        }

        return result;
    }

    /**
     * 格式化异常时间显示
     * 只有迟到、早退、时长异常三种类型显示具体时间，其他显示"-"
     *
     * @param abnormalType 异常类型
     * @param timeMinutes  异常时间（分钟）
     * @return 格式化后的时间字符串
     */
    private String formatAbnormalTime(String abnormalType, BigDecimal timeMinutes) {
        // 只有特定类型显示时间
        if (!AttendanceAbnormalTypeEnum.LATE.getCode().equals(abnormalType) &&
                !AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode().equals(abnormalType) &&
                !AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode().equals(abnormalType)) {
            return "-";
        }

        if (timeMinutes == null || timeMinutes.compareTo(BigDecimal.ZERO) <= 0) {
            return "-";
        }

        return timeMinutes.intValue() + "min";
    }

}
