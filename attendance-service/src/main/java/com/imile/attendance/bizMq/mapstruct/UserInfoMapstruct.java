package com.imile.attendance.bizMq.mapstruct;

import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description
 */
@Mapper
public interface UserInfoMapstruct {

    UserInfoMapstruct INSTANCE = Mappers.getMapper(UserInfoMapstruct.class);


    UserInfoDO mapToAttendanceUserInfoDO(HrmsUserInfoDO hrmsUserInfoDO);

    List<UserInfoDO> mapToAttendanceUserInfoDO(List<HrmsUserInfoDO> hrmsUserInfoDOList);


    UserEntryRecordDO mapToAttendanceUserEntryRecordDO(HrmsUserEntryRecordDO hrmsUserEntryRecordDO);

    List<UserEntryRecordDO> mapToAttendanceUserEntryRecordDO(List<HrmsUserEntryRecordDO> hrmsUserEntryRecordDOList);


    UserDimissionRecordDO mapToAttendanceUserDimissionRecordDO(HrmsUserDimissionRecordDO hrmsUserDimissionRecordDO);

    List<UserDimissionRecordDO> mapToAttendanceUserDimissionRecordDO(List<UserDimissionRecordDO> hrmsUserDimissionRecordDOList);

    @Mapping(target = "id", ignore = true)
    void updateAttendanceUserInfo(@MappingTarget UserInfoDO target, HrmsUserInfoDO source);

    @Mapping(target = "id", ignore = true)
    void updateAttendanceUserEntryRecord(@MappingTarget UserEntryRecordDO target, HrmsUserEntryRecordDO source);

    @Mapping(target = "id", ignore = true)
    void updateAttendanceUserDimissionRecord(@MappingTarget UserDimissionRecordDO target, HrmsUserDimissionRecordDO source);
}
