package com.imile.attendance.rule.application;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.EntryStatusEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.loader.StrategyLoader;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.command.PunchClassConfigAddCommand;
import com.imile.attendance.rule.command.PunchClassConfigStatusSwitchCommand;
import com.imile.attendance.rule.dto.PunchClassConfigAddDTO;
import com.imile.attendance.rule.mapstruct.PunchClassConfigApiMapstruct;
import com.imile.attendance.rule.service.PunchClassConfigService;
import com.imile.attendance.rule.vo.PunchClassConfigAddConfirmVO;
import com.imile.attendance.rule.vo.PunchClassConfigAddVO;
import com.imile.attendance.rule.vo.PunchClassConfigDisabledCheckConfirmVO;
import com.imile.attendance.rule.vo.PunchClassConfigUpdateConfirmVO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Slf4j
@Service
public class PunchClassConfigApplicationService {

    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    protected PunchClassConfigRangeDao punchClassConfigRangeDao;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    protected CountryService countryService;
    @Resource
    private MigrationService migrationService;


    /**
     * 班次新增前置处理
     */
    public PunchClassConfigAddConfirmVO addPreProcessor(PunchClassConfigAddCommand command) {
        checkClassNature(command.getClassNature());
        PunchClassConfigAddDTO dto = PunchClassConfigApiMapstruct.INSTANCE.toAddDTO(command);
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(command.getClassNature())));
        return classConfigService.addPreProcessor(dto);
    }


    /**
     * 班次新增
     */
    public PunchClassConfigAddVO add(PunchClassConfigAddCommand command) {
        List<String> enableNewAttendanceCountry = migrationService.getEnableNewAttendanceCountry();
        if (!enableNewAttendanceCountry.contains(command.getCountry())) {
            throw BusinessLogicException.getException(
                    ErrorCodeEnum.THE_NON_GRAY_SCALE_COUNTRY_NEED_TO_OPERATE_IN_THE_HRMS_SYSTEM, command.getCountry());
        }
        checkClassNature(command.getClassNature());
        PunchClassConfigAddDTO dto = PunchClassConfigApiMapstruct.INSTANCE.toAddDTO(command);
        //策略匹配班次实现
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(command.getClassNature())));
        return classConfigService.add(dto);
    }


    /**
     * 班次编辑前置处理
     */
    public PunchClassConfigUpdateConfirmVO updatePreProcessor(PunchClassConfigAddCommand command) {
        PunchClassConfigAddDTO dto = PunchClassConfigApiMapstruct.INSTANCE.toAddDTO(command);
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(command.getClassNature())));
        return classConfigService.updatePreProcessor(dto);
    }


    /**
     * 班次编辑
     */
    public PunchClassConfigAddVO update(PunchClassConfigAddCommand command) {
        List<String> enableNewAttendanceCountry = migrationService.getEnableNewAttendanceCountry();
        if (!enableNewAttendanceCountry.contains(command.getCountry())) {
            throw BusinessLogicException.getException(
                    ErrorCodeEnum.THE_NON_GRAY_SCALE_COUNTRY_NEED_TO_OPERATE_IN_THE_HRMS_SYSTEM, command.getCountry());
        }
        checkClassNature(command.getClassNature());
        PunchClassConfigAddDTO dto = PunchClassConfigApiMapstruct.INSTANCE.toAddDTO(command);
        //策略匹配班次实现
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(command.getClassNature())));
        return classConfigService.update(dto);
    }


    /**
     * 班次停用检查
     */
    public PunchClassConfigDisabledCheckConfirmVO disabledCheck(Long classId) {
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(ClassNatureEnum.FIXED_CLASS.name())));
        return classConfigService.disabledCheck(classId);

    }


    /**
     * 班次启用检查
     */
    public void enableCheck(Long classId) {
        PunchClassConfigDTO punchClassConfigDTO = Optional.ofNullable(punchClassConfigManage.selectById(classId))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.CLASS_NOT_EXISTS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NOT_EXISTS.getDesc())));
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(punchClassConfigDTO.getClassNature())));
        classConfigService.enableCheck(punchClassConfigDTO);
    }


    /**
     * 班次状态启停用
     */
    public Boolean statusSwitch(PunchClassConfigStatusSwitchCommand command) {
        PunchClassConfigDO punchClassConfig = Optional.ofNullable(punchClassConfigDao.selectById(command.getId()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.CLASS_NOT_EXISTS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NOT_EXISTS.getDesc())));
        List<String> enableNewAttendanceCountry = migrationService.getEnableNewAttendanceCountry();
        if (!enableNewAttendanceCountry.contains(punchClassConfig.getCountry())) {
            throw BusinessLogicException.getException(
                    ErrorCodeEnum.THE_NON_GRAY_SCALE_COUNTRY_NEED_TO_OPERATE_IN_THE_HRMS_SYSTEM, punchClassConfig.getCountry());
        }
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(punchClassConfig.getClassNature())));
        if (Objects.equals(StatusEnum.DISABLED.getCode(), command.getStatus())) {
            return classConfigService.disabledStatus(command.getId());
        }
        return classConfigService.enableStatus(command.getId());
    }


    /**
     * 用户班次性质转换后班次联动处理
     *
     * @param userId 用户ID
     */
    public void classNatureSwitchHandler(Long userId,
                                         String oldClassNature,
                                         String newClassNature,
                                         DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        if (Objects.isNull(userId)
//                || StringUtils.isEmpty(oldClassNature)
                || StringUtils.isEmpty(newClassNature)
                || Objects.equals(newClassNature, oldClassNature)) {
            return;
        }
        UserInfoDO userInfoDO = Optional.ofNullable(userInfoDao.getByUserId(userId))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));
        if (!Objects.equals(StatusEnum.ACTIVE.getCode(), userInfoDO.getStatus())
                || !Objects.equals(WorkStatusEnum.ON_JOB.getCode(), userInfoDO.getWorkStatus())
                || !Objects.equals(BusinessConstant.N, userInfoDO.getIsDriver())) {
            log.error("考勤用户范围异常,userCode:{},status:{},workStatus:{},isDriver:{}", userInfoDO.getUserCode(), userInfoDO.getStatus(), userInfoDO.getWorkStatus(), userInfoDO.getIsDriver());
            return;
        }
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(newClassNature)));
        classConfigService.classNatureSwitchHandler(userInfoDO, oldClassNature, newClassNature, currentDateAndTimeZoneDate);
    }

    /**
     * 用户入职自动加入班次和排班
     *
     * @param userCode 用户编码
     */
    public void userEntryAutoShift(String userCode, AttendanceUserEntryRecord attendanceUserEntryRecord) {
        UserInfoDO userInfo = userInfoDao.getByUserCode(userCode);
        if (Objects.isNull(userInfo)) {
            log.error("{}: 用户入职排班处理：用户查询为空", userCode);
            return;
        }
        if (!migrationService.verifyUserIsEnableAttendanceForCountry(userInfo.getId())) {
            log.info("userEntryAutoShift | userInfo is not in new attendance country, userCode:{}", userInfo.getUserCode());
            return;
        }
        if (Objects.isNull(attendanceUserEntryRecord) || !Objects.equals(EntryStatusEnum.ENTRY.getCode(), attendanceUserEntryRecord.getEntryStatus())) {
            log.info("{}: 用户入职排班处理：入职记录筛选为空", userInfo.getUserCode());
            return;
        }
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(userInfo.getClassNature())));
        classConfigService.userEntryAutoShift(userInfo, attendanceUserEntryRecord);
    }


    /**
     * 用户离职更新班次适用范围
     */
    public void updatePunchClassConfigRange(UserInfoDO userInfoDO,
                                            Date actualDimissionDate,
                                            boolean isDelayConfirm) {
        if (!migrationService.verifyUserIsEnableAttendanceForCountry(userInfoDO.getId())) {
            log.info("updatePunchClassConfigRange | userInfo is not in new attendance country, userCode:{}", userInfoDO.getUserCode());
            return;
        }
        Date finalDateNow = DateUtil.endOfDay(actualDimissionDate);
        List<PunchClassConfigRangeDO> punchClassConfigRangeList = punchClassConfigRangeDao.selectByBizIdsAndStatus(Collections.singletonList(userInfoDO.getId()), StatusEnum.ACTIVE.getCode());

        List<PunchClassConfigRangeDO> filterPunchClassConfigRangeList = punchClassConfigRangeList.stream()
                .filter(item -> item.getEffectTime().compareTo(finalDateNow) < 1 && item.getExpireTime().compareTo(finalDateNow) > -1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterPunchClassConfigRangeList)) {
            return;
        }

        filterPunchClassConfigRangeList.forEach(config -> {
            config.setExpireTime(finalDateNow);
            config.setExpireTimestamp(finalDateNow.getTime());
            //延后离职直接更新状态未历史版本，当天离职还需要处理异常保留最新版本
            config.setIsLatest(isDelayConfirm ? BusinessConstant.N : BusinessConstant.Y);
            BaseDOUtil.fillDOUpdateByUserOrSystem(config);
        });

        List<PunchClassConfigRangeDO> updatePunchClassConfigRangeList = new ArrayList<>(filterPunchClassConfigRangeList);
        Long tempMaxId = filterPunchClassConfigRangeList.get(filterPunchClassConfigRangeList.size() - 1).getId();

        //存在多次修改删除后面的所有版本
        List<PunchClassConfigRangeDO> expireClassConfigRangeList = punchClassConfigRangeList.stream().filter(range -> range.getId().compareTo(tempMaxId) > 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expireClassConfigRangeList)) {
            expireClassConfigRangeList.forEach(range -> {
                range.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
            updatePunchClassConfigRangeList.addAll(expireClassConfigRangeList);
        }
        punchClassConfigRangeDao.updateBatchById(updatePunchClassConfigRangeList);

    }

    /**
     * 员工国家或部门变动班次重新绑定处理
     */
    public void classSchedulingHandler(CalendarAndPunchHandlerDTO classHandlerDTO) {
        log.info("classSchedulingHandler:{}", JSON.toJSON(classHandlerDTO));
//        if (!migrationService.verifyUserIsEnableAttendanceForCountry(classHandlerDTO.getUserId())) {
//            log.info("doNotice2Attendance | userInfo is not in new attendance country, userId:{}", classHandlerDTO.getUserId());
//            return;
//        }
        if (classHandlerDTO.getUserId() == null
                || classHandlerDTO.getNewDeptId() == null
                || classHandlerDTO.getOldDeptId() == null
                || StringUtils.isEmpty(classHandlerDTO.getUserCode())
                || StringUtils.isBlank(classHandlerDTO.getNewCountry())
                || StringUtils.isBlank(classHandlerDTO.getOldCountry())) {
            log.info("classSchedulingHandler | 参数异常，handlerDTO:{}", JSON.toJSON(classHandlerDTO));
            return;
        }

        List<UserInfoDO> userInfoDOList = userInfoManage.selectByUserCodes(Collections.singletonList(classHandlerDTO.getUserCode()));
        if (CollectionUtils.isEmpty(userInfoDOList) || StringUtils.isEmpty(userInfoDOList.get(0).getClassNature())) {
            log.info("classSchedulingHandler 查询员工不符合班次条件");
            return;
        }

        UserInfoDO userInfoDO = userInfoDOList.get(0);
        PunchClassConfigService classConfigService = StrategyLoader.load(PunchClassConfigService.class, t -> t.isMatch(ClassNatureEnum.getByCode(userInfoDO.getClassNature())));
        classConfigService.classSchedulingHandler(classHandlerDTO, userInfoDO.getClassNature());
    }

    /**
     * 班次性质有效性检查
     */
    private void checkClassNature(String classNature) {
        if (Objects.isNull(ClassNatureEnum.getByCode(classNature))) {
            throw BusinessException.get(ErrorCodeEnum.CLASS_NATURE_ILLEGALITY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NATURE_ILLEGALITY.getDesc()));
        }
    }

    public void refreshPunchClassRangeTimestamp() {
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectAll();
        for (PunchClassConfigDO punchClassConfigDO : punchClassConfigDOList) {
            CountryDTO countryDTO = countryService.queryCountry(punchClassConfigDO.getCountry());
            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectByRuleConfigIds(Collections.singletonList(punchClassConfigDO.getId()));
            for (PunchClassConfigRangeDO punchClassConfigRangeDO : punchClassConfigRangeDOList) {
                punchClassConfigRangeDO.setEffectTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(countryDTO.getTimeZone(), punchClassConfigRangeDO.getEffectTime()));
                punchClassConfigRangeDO.setExpireTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(countryDTO.getTimeZone(), punchClassConfigRangeDO.getExpireTime()));
            }
            punchClassConfigRangeDao.updateBatchById(punchClassConfigRangeDOList);
        }
    }

}
