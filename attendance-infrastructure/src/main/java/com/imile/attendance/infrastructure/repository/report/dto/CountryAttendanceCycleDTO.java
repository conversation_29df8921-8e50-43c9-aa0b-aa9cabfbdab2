package com.imile.attendance.infrastructure.repository.report.dto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/08/06
 * @Time 15:53
 * @Description
 */
@Builder
@Data
public class CountryAttendanceCycleDTO {

    private String country;

    private String cycleStartDate;

    private String cycleEndDate;

    /**
     * 周期起始时间
     */
    private Long attendanceStartCycle;

    /**
     * 周期截止时间
     */
    private Long attendanceEndCycle;

    /**
     * 周期开始
     */
    private String cycleStart;

    /**
     * 周期结束
     */
    private String cycleEnd;

}
