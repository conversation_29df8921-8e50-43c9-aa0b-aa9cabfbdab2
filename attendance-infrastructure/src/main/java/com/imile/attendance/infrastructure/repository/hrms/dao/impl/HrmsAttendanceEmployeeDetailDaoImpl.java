package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsAttendanceEmployeeDetailMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 员工出勤明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsAttendanceEmployeeDetailDaoImpl extends ServiceImpl<HrmsAttendanceEmployeeDetailMapper, HrmsAttendanceEmployeeDetailDO> implements HrmsAttendanceEmployeeDetailDao {


    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectByCondition(AbnormalMigrationQuery query, Long lastId) {
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(Objects.nonNull(lastId), HrmsAttendanceEmployeeDetailDO::getId,lastId);
        wrapper.ge(Objects.nonNull(query.getStartDayId()), HrmsAttendanceEmployeeDetailDO::getDayId, query.getStartDayId());
        wrapper.le(Objects.nonNull(query.getEndDayId()), HrmsAttendanceEmployeeDetailDO::getDayId, query.getEndDayId());
        wrapper.in(CollectionUtils.isNotEmpty(query.getUserIdList()), HrmsAttendanceEmployeeDetailDO::getUserId, query.getUserIdList());
        wrapper.in(CollectionUtils.isNotEmpty(query.getDeptIdList()), HrmsAttendanceEmployeeDetailDO::getDeptId, query.getDeptIdList());
        wrapper.in(CollectionUtils.isNotEmpty(query.getCountryList()), HrmsAttendanceEmployeeDetailDO::getLocationCountry, query.getCountryList());
        wrapper.orderByAsc(HrmsAttendanceEmployeeDetailDO::getId);
        wrapper.last("limit 1000");
        return this.list(wrapper);
    }
}
