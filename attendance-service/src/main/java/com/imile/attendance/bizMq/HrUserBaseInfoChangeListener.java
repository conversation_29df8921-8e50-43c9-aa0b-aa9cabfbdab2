package com.imile.attendance.bizMq;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.calendar.CalendarConfigService;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.infrastructure.mq.BaseRocketMQListener;
import com.imile.attendance.bizMq.constant.UserInfoFieldEnum;
import com.imile.attendance.bizMq.mapstruct.UserInfoMapstruct;
import com.imile.attendance.bizMq.sync.SyncHrToAttendanceTableService;
import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.mq.helper.OperatorHelper;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.RuleConfigRangeService;
import com.imile.attendance.rule.application.PunchClassConfigApplicationService;
import com.imile.attendance.shift.UserShiftService;
import com.imile.attendance.shift.dto.EmployeeSchedulingHandlerDTO;
import com.imile.attendance.third.ThirdZktecoService;
import com.imile.attendance.user.UserLeaveService;
import com.imile.attendance.user.UserService;
import com.imile.attendance.vacation.DispatchUserRecordService;
import com.imile.hrms.api.base.component.DifferField;
import com.imile.hrms.api.user.enums.UserEventTagsEnum;
import com.imile.hrms.api.user.param.UserEventParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26
 * @Description 监听用户基础信息变更消息 || 监听派遣变动消息
 */
@Slf4j
@Component
@RocketMQMessageListener(
        nameServer = "${rocketmq.nameServer}",
        topic = "${rocket.mq.hr.user.topic}",
        consumerGroup = "${rocket.mq.hr.user.baseInfo.change.group}",
        selectorExpression = "${rocket.mq.hr.user.baseInfo.change.tag} || ${rocket.mq.hr.transform.tag}",
        consumeThreadMax = 1)
public class HrUserBaseInfoChangeListener extends BaseRocketMQListener {

    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private CalendarConfigService calendarConfigService;
    @Resource
    private ThirdZktecoService thirdZktecoService;
    @Resource
    private SyncHrToAttendanceTableService syncHrToAttendanceTableService;
    @Resource
    private UserShiftService userShiftService;
    @Resource
    private RuleConfigRangeService ruleConfigRangeService;
    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private CalendarConfigDao calendarConfigDao;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private PunchClassConfigApplicationService punchClassConfigApplicationService;
    @Resource
    private UserLeaveService userLeaveService;
    @Resource
    private UserService userService;
    @Resource
    private DispatchUserRecordService dispatchUserRecordService;
    @Resource
    private AttendanceUserService attendanceUserService;
    @Resource
    private MigrationService migrationService;

    @Override
    public void doOnMessage(MessageExt messageExt) {
        String operateTags = messageExt.getTags();
        UserEventParam<?> param = JSON.parseObject(new String(messageExt.getBody()), UserEventParam.class);
        if (UserEventTagsEnum.BASE_INFO_CHANGE.getCode().equals(operateTags)) {
            log.info("收到人员变动消息,msgId:{},topic:{},tags:{},param:{}",
                    messageExt.getMsgId(), messageExt.getTopic(), messageExt.getTags(), JSON.toJSONString(param));
            doBaseInfoChangeEvent(param);
        } else if (UserEventTagsEnum.TRANSFORM_NOTICE.getCode().equals(operateTags)) {
            log.info("收到派遣人员变动消息,msgId:{},topic:{},tags:{},param:{}",
                    messageExt.getMsgId(), messageExt.getTopic(), messageExt.getTags(), JSON.toJSONString(param));
            doTransformEvent(param);
        }
    }

    /**
     * 常驻国变化事件处理
     *
     * @param param
     */
    private void doBaseInfoChangeEvent(UserEventParam<?> param) {
        UserEventParam.BaseInfoChange baseInfoChange
                = JSON.parseObject(JSON.toJSONString(param.getBody()), UserEventParam.BaseInfoChange.class);
        OperatorHelper.putOperatorInfo(param.getOperator());
        String userCode = param.getUserCode();
        Long timestamp = param.getTimestamp();
        log.info("收到人员变动消息，userCode:{},timestamp:{}", userCode, timestamp);
        //1.更新考勤用户表信息
        UserInfoDO userInfoDO = userInfoDao.getByUserCode(userCode);
        if (userInfoDO == null) {
            log.info("考勤人员信息表中不存在该人员，userCode:{},触发同步hr人员操作", userCode);
            //可能需要立即触发同步操作
            syncHrToAttendanceTableService.syncUserEntryRecord(userCode);
            HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoDao.getByCode(userCode);
            userInfoDO = UserInfoMapstruct.INSTANCE.mapToAttendanceUserInfoDO(hrmsUserInfoDO);
        } else {
            syncHrToAttendanceTableService.syncUserInfoRecordWithHrUser(userInfoDO);
        }
        attendanceUserService.invalidateUserCache(userCode);

        //2.实现原有的doNotice2Attendance逻辑
        List<DifferField> changeFieldList = baseInfoChange.getChangeFieldList();
        if (CollectionUtils.isEmpty(changeFieldList)) {
            log.error("收到人员变动消息，userCode:{},变动字段列表为空", userCode);
            return;
        }
        doNotice2Attendance(userInfoDO, changeFieldList, timestamp);
        //3.实现原有的doNotice2Leave逻辑
        doNotice2Leave(userInfoDO, changeFieldList);
        //4.实现原有的同步员工数据到中控考勤系统逻辑
        thirdZktecoService.syncEmployee2Zkteco(userInfoDO.getId());
    }

    /**
     * 员工调动事件处理
     *
     * @param param
     */
    private void doTransformEvent(UserEventParam<?> param) {
        UserEventParam.TransformNotice transform = JSON.parseObject(JSON.toJSONString(param.getBody()), UserEventParam.TransformNotice.class);
        dispatchUserRecordService.handleDispatchNotice(transform);
    }

    private void doNotice2Attendance(UserInfoDO user, List<DifferField> fieldDifferList, Long timestamp) {
        DifferField deptIdFieldDiffer = fieldDifferList.stream()
                .filter(s -> UserInfoFieldEnum.DEPT_ID.getKey().equals(s.getFieldName()))
                .findAny()
                .orElse(null);
        DifferField locationCountryFieldDiffer = fieldDifferList.stream()
                .filter(s -> UserInfoFieldEnum.LOCATION_COUNTRY.getKey().equals(s.getFieldName()))
                .findAny()
                .orElse(null);
        if (Objects.isNull(deptIdFieldDiffer) && Objects.isNull(locationCountryFieldDiffer)) {
            return;
        }
        CalendarAndPunchHandlerDTO calendarAndPunchHandlerDTO = new CalendarAndPunchHandlerDTO();
        calendarAndPunchHandlerDTO.setUserId(user.getId());
        calendarAndPunchHandlerDTO.setUserCode(user.getUserCode());
        if (Objects.nonNull(deptIdFieldDiffer)) {
            calendarAndPunchHandlerDTO.setOldDeptId(Long.parseLong(deptIdFieldDiffer.getBeforeValue().toString()));
            calendarAndPunchHandlerDTO.setNewDeptId(Long.parseLong(deptIdFieldDiffer.getAfterValue().toString()));
        } else {
            calendarAndPunchHandlerDTO.setNewDeptId(user.getDeptId());
            calendarAndPunchHandlerDTO.setOldDeptId(user.getDeptId());
        }
        if (Objects.nonNull(locationCountryFieldDiffer)) {
            calendarAndPunchHandlerDTO.setOldCountry(locationCountryFieldDiffer.getBeforeValue().toString());
            calendarAndPunchHandlerDTO.setNewCountry(locationCountryFieldDiffer.getAfterValue().toString());
        } else {
            calendarAndPunchHandlerDTO.setNewCountry(user.getLocationCountry());
            calendarAndPunchHandlerDTO.setOldCountry(user.getLocationCountry());
        }
        calendarAndPunchHandlerDTO.setCurrentDate(new Date(timestamp == null ? System.currentTimeMillis() : timestamp));

        if (userService.checkValidUserAttendanceRange(calendarAndPunchHandlerDTO.getUserId())) {

            //日历处理
            calendarConfigService.userCalendarHandler(calendarAndPunchHandlerDTO);

            //考勤规则处理
            ruleConfigRangeService.userRuleConfigRangeHandler(calendarAndPunchHandlerDTO);

            //班次处理
            punchClassConfigApplicationService.classSchedulingHandler(calendarAndPunchHandlerDTO);

            //排班处理
            EmployeeSchedulingHandlerDTO handlerDTO = buildEmployeeSchedulingHandlerDTO(user);
            if (Objects.nonNull(handlerDTO)) {
                log.info("doNotice2Attendance|employeeSchedulingHandlerDTO:{}", handlerDTO);
                userShiftService.employeeSchedulingHandler(Collections.singletonList(handlerDTO));
            }
        } else {
            log.info("用户基础信息变更,该用户不在考勤有效用户范围中:{}", user.getUserCode());
        }

        //假期处理
        userLeaveService.userLeaveConfigRangeHandler(user);
    }

    private void doNotice2Leave(UserInfoDO user, List<DifferField> fieldDifferList) {
        DifferField employeeTypeFieldDiffer = fieldDifferList.stream()
                .filter(s -> UserInfoFieldEnum.EMPLOYEE_TYPE.getKey().equals(s.getFieldName()))
                .findAny()
                .orElse(null);
        DifferField sexFieldDiffer = fieldDifferList.stream()
                .filter(s -> UserInfoFieldEnum.SEX.getKey().equals(s.getFieldName()))
                .findAny()
                .orElse(null);
        DifferField countryCodeFieldDiffer = fieldDifferList.stream()
                .filter(s -> UserInfoFieldEnum.COUNTRY_CODE.getKey().equals(s.getFieldName()))
                .findAny()
                .orElse(null);
        if (Objects.isNull(employeeTypeFieldDiffer) && Objects.isNull(sexFieldDiffer)
                && Objects.isNull(countryCodeFieldDiffer)) {
            return;
        }
        // 如果员工类型+性别没有变动, 则需要判断国籍是否变动和当前人员是否派遣
        if (Objects.isNull(employeeTypeFieldDiffer)
                && Objects.isNull(sexFieldDiffer)
                && Objects.nonNull(countryCodeFieldDiffer)
                && !BusinessConstant.Y.equals(user.getIsGlobalRelocation())) {
            return;
        }
        // 处理假期数据
        userLeaveService.userLeaveConfigRangeHandler(user);
    }

    public EmployeeSchedulingHandlerDTO buildEmployeeSchedulingHandlerDTO(UserInfoDO user) {
//        if (!migrationService.verifyUserIsEnableAttendanceForCountry(user.getId())) {
//            log.info("doNotice2Attendance | userInfo is not in new attendance country, userId:{}", user.getId());
//            return null;
//        }
        if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), user.getClassNature())) {
            return null;
        }
        Long userId = user.getId();
        //查询当前用户配置的考勤日历
        List<CalendarConfigRangeDO> calendarConfigRangeDOList = calendarConfigRangeDao.selectConfigRange(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(calendarConfigRangeDOList)) {
            log.info("doNotice2Attendance|calendarConfigRangeDOList is empty,userId:{}", user.getId());
            return null;
        }

        List<CalendarConfigDO> calendarConfigDOList = calendarConfigDao.getByCalendarConfigIds(Collections.singletonList(calendarConfigRangeDOList.get(0).getAttendanceConfigId()));
        if (CollectionUtils.isEmpty(calendarConfigDOList)) {
            log.info("doNotice2Attendance|calendarConfigDOList is empty,userId:{}", userId);
            return null;
        }

        Map<Long, PunchClassConfigDO> punchClassConfigMap = punchClassConfigManage.selectTopPriorityByUserIds(Collections.singletonList(userId));
        if (MapUtils.isEmpty(punchClassConfigMap)) {
            log.info("doNotice2Attendance|userClassConfigDTOList is empty,userId:{}", userId);
            return null;
        }

        EmployeeSchedulingHandlerDTO handlerDTO = new EmployeeSchedulingHandlerDTO();
        handlerDTO.setUserId(user.getId());
        handlerDTO.setStartDate(new Date());
        handlerDTO.setCalendarConfigId(calendarConfigDOList.get(0).getId());
        handlerDTO.setPunchClassConfigId(punchClassConfigMap.get(userId).getId());
        return handlerDTO;
    }
}
