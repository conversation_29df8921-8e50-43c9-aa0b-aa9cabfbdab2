package com.imile.attendance.infrastructure.repository.cycleConfig.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleCalendarDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.mapper.AttendanceCycleCalendarMapper;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleCalendarDO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> chen
 * @Date 2025/08/11
 * @Time 10:57
 * @Description
 */
@Service
public class AttendanceCycleCalendarDaoImpl extends ServiceImpl<AttendanceCycleCalendarMapper, AttendanceCycleCalendarDO>
        implements AttendanceCycleCalendarDao {
}
