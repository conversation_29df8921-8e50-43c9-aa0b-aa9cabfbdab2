<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.cycleConfig.mapper.AttendanceCycleCalendarMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleCalendarDO">
        <!-- 主键映射 -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="attendance_year" property="attendanceYear" jdbcType="INTEGER"/>
        <result column="attendance_month" property="attendanceMonth" jdbcType="INTEGER"/>
        <result column="cycle_start_date" property="cycleStartDate" jdbcType="TIMESTAMP"/>
        <result column="cycle_end_date" property="cycleEndDate" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="record_version" property="recordVersion" jdbcType="INTEGER"/>
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="create_user_code" property="createUserCode" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="last_upd_date" property="lastUpdDate" jdbcType="TIMESTAMP"/>
        <result column="last_upd_user_code" property="lastUpdUserCode" jdbcType="VARCHAR"/>
        <result column="last_upd_user_name" property="lastUpdUserName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列列表 -->
    <sql id="Base_Column_List">
        id,
        country,
        attendance_year,
        attendance_month,
        cycle_start_date,
        cycle_end_date,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name
    </sql>

</mapper>