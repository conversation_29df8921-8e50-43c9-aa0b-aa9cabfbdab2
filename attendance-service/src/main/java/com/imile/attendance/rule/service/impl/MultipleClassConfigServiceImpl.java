package com.imile.attendance.rule.service.impl;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigRangeDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.rule.dto.PunchClassConfigAddAutoShiftDTO;
import com.imile.attendance.rule.dto.PunchClassConfigAddDTO;
import com.imile.attendance.rule.dto.PunchClassConfigRangeDifferDTO;
import com.imile.attendance.rule.dto.PunchClassConfigSwitchStatusDTO;
import com.imile.attendance.rule.service.PunchClassConfigAbstractService;
import com.imile.attendance.rule.service.PunchClassConfigService;
import com.imile.attendance.rule.vo.PunchClassConfigAddVO;
import com.imile.attendance.rule.vo.PunchClassConfigUpdateConfirmVO;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 多班次实现
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Strategy(value = PunchClassConfigService.class, implKey = "MultipleClassConfigServiceImpl")
public class MultipleClassConfigServiceImpl extends PunchClassConfigAbstractService {
    /**
     * 特殊国家过滤
     * 要求班次类型必填
     */
    private static final List<String> SPECIAL_COUNTRY_FILTER = Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode());

    @Override
    public boolean isMatch(ClassNatureEnum classNatureEnum) {
        return ClassNatureEnum.MULTIPLE_CLASS.equals(classNatureEnum);
    }

    @Override
    public void userEntryAutoShift(UserInfoDO userInfo, AttendanceUserEntryRecord attendanceUserEntryRecord) {
        List<PunchClassConfigDTO> punchClassConfigDTOList = punchClassConfigManage.selectByCountries(userInfo.getLocationCountry(), userInfo.getClassNature());
        if (CollectionUtils.isEmpty(punchClassConfigDTOList)) {
            log.info("{}: 用户入职排班处理：当前无班次规则无需处理", userInfo.getUserCode());
            return;
        }

        //实际确认入职时间
        Date confirmDate = attendanceUserEntryRecord.getConfirmDate();
        DateAndTimeZoneDate currentDateAndTimeZoneDate = DateAndTimeZoneDate.of(confirmDate, confirmDate);

        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = new ArrayList<>();
        for (PunchClassConfigDTO classConfigDTO : punchClassConfigDTOList) {
            if (!classConfigDTO.countryLevel() && !classConfigDTO.convertDeptList().contains(userInfo.getDeptId())) {
                continue;
            }
            String rangeType = Objects.equals(BusinessConstant.Y, classConfigDTO.getIsCountryLevel()) ? RuleRangeTypeEnum.COUNTRY.getCode() : RuleRangeTypeEnum.DEPT.getCode();
            build(Collections.singletonList(userInfo.getId()), currentDateAndTimeZoneDate, rangeType, classConfigDTO.getId(), classConfigDTO.getConfigNo(), "员工入职绑定班次", punchClassConfigRangeDOList);
        }
        if (CollectionUtils.isNotEmpty(punchClassConfigRangeDOList)) {
            punchClassConfigRangeDao.saveBatch(punchClassConfigRangeDOList);
        }
    }


    @Override
    protected void buildChangeClassRangeCount(Boolean isClassInfoUpdate,
                                              PunchClassConfigDTO oldDto,
                                              PunchClassConfigAddDTO newDto,
                                              PunchClassConfigUpdateConfirmVO result) {
        if (isClassInfoUpdate) {
            result.setItemInfoUpdate(Boolean.TRUE);
            Long preDayId = DateHelper.getPreviousDayId(getDayIdByCountryTimeZone(oldDto.getCountry()));
            List<Long> userIds = userShiftConfigManage.getUserIdsByPunchClassIdAndAfterDayId(oldDto.getId(), preDayId);
            result.setEmployeeSchedulingCount(userIds.size());
        }

        //旧的班次范围是国家级别
        if (oldDto.countryLevel()) {
            if (!newDto.countryLevel()) {
                //旧的班次国家适用范围人数
                Set<Long> oldCountryRangeUserIds = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                        .stream().map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());
                result.setRemoveRangeEmployeeCount(oldCountryRangeUserIds.size());
                result.setRangeUpdate(Boolean.TRUE);
            }
            return;
        }

        //新的班次范围为国家级别且旧的班次范围不为国家级
        if (newDto.countryLevel()) {
            Set<Long> oldUserIdSet = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                    .stream().map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());
            result.setRemoveRangeEmployeeCount(oldUserIdSet.size());
            result.setRangeUpdate(Boolean.TRUE);
            return;
        }

        //新旧都不为国家级范围
        buildClassRangeDifferCount(oldDto, newDto, result);
    }

    @Override
    protected void bindNewClassRange(UserInfoDO userInfoDO, DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        //添加到新的班次适用范围中
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestAndActiveByCountry(Collections.singletonList(userInfoDO.getLocationCountry()), userInfoDO.getClassNature());
        if (CollectionUtils.isNotEmpty(punchClassConfigDOList)) {
            List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList = new ArrayList<>();
            for (PunchClassConfigDO punchClassConfigDO : punchClassConfigDOList) {
                if (Objects.equals(punchClassConfigDO.getIsCountryLevel(), BusinessConstant.N) && StringUtils.isEmpty(punchClassConfigDO.getDeptIds())) {
                    continue;
                }

                if (Objects.equals(punchClassConfigDO.getIsCountryLevel(), BusinessConstant.N) && StringUtils.isNotBlank(punchClassConfigDO.getDeptIds())) {
                    List<Long> deptIdList = Arrays.stream(punchClassConfigDO.getDeptIds().split(BusinessConstant.DEFAULT_DELIMITER))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::valueOf)
                            .collect(Collectors.toList());
                    if (!deptIdList.contains(userInfoDO.getDeptId())) {
                        continue;
                    }
                }

                String rangeType = Objects.equals(BusinessConstant.Y, punchClassConfigDO.getIsCountryLevel()) ? RuleRangeTypeEnum.COUNTRY.getCode() : RuleRangeTypeEnum.DEPT.getCode();
                build(Collections.singletonList(userInfoDO.getId()), currentDateAndTimeZoneDate, rangeType, punchClassConfigDO.getId(), punchClassConfigDO.getConfigNo(), "员工班次性质切换绑定班次", addPunchClassConfigRangeDOList);
            }
            punchClassConfigRangeDao.saveBatch(addPunchClassConfigRangeDOList);
        }
    }

    @Override
    protected void bindNewClassRangeList(CalendarAndPunchHandlerDTO classHandlerDto,
                                         List<PunchClassConfigDTO> newPunchClassConfigList,
                                         List<PunchClassConfigRangeDO> addRunchClassConfigRangeList) {
        if (CollectionUtils.isEmpty(newPunchClassConfigList)) {
            return;
        }
        CountryDTO countryDTO = countryService.queryCountry(classHandlerDto.getNewCountry());
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(classHandlerDto.getCurrentDate());

        //绑定新的非用户级别适用范围班次
        newPunchClassConfigList.forEach(newRecord -> {
            String rangeType = newRecord.countryLevel() ? RuleRangeTypeEnum.COUNTRY.getCode() : RuleRangeTypeEnum.DEPT.getCode();
            if (newRecord.countryLevel()) {
                build(Collections.singletonList(classHandlerDto.getUserId()), currentDateAndTimeZoneDate, rangeType, newRecord.getId(), newRecord.getConfigNo(), "员工国家或部门变动", addRunchClassConfigRangeList);
                return;
            }

            if (!newRecord.convertDeptList().contains(classHandlerDto.getNewDeptId())) {
                return;
            }
            build(Collections.singletonList(classHandlerDto.getUserId()), currentDateAndTimeZoneDate, rangeType, newRecord.getId(), newRecord.getConfigNo(), "员工国家或部门变动", addRunchClassConfigRangeList);
        });

    }

    @Override
    protected PunchClassConfigAddVO customCheck(PunchClassConfigAddDTO dto) {
        //班次类型
        if (SPECIAL_COUNTRY_FILTER.contains(dto.getCountry()) && Objects.isNull(dto.getClassType())) {
            throw BusinessException.get(ErrorCodeEnum.CLASS_TYPE_CANNOT_BE_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_TYPE_CANNOT_BE_EMPTY.getDesc()));
        }
        return new PunchClassConfigAddVO();
    }

    @Override
    protected PunchClassConfigAddAutoShiftDTO buildPunchClassConfigAddAutoShiftDTO(PunchClassConfigAddDTO dto) {
        return new PunchClassConfigAddAutoShiftDTO();
    }

    @Override
    protected void addPostProcessor(PunchClassConfigAddAutoShiftDTO classConfigAddAutoShiftDTO) {
        //多班次暂无需要后置处理的场景 空实现
    }

    @Override
    protected void classRangeCustomCheck(PunchClassConfigDTO classConfigDTO, List<PunchClassConfigDTO> punchClassConfigDTOList) {
        //多班次暂无需要校验 空实现
    }

    @Override
    protected void switchStatusPostProcessor(Long classId, Set<Long> userIds, String status) {
        //多班次启用不涉及自动排班
        if (CollectionUtils.isEmpty(userIds) || Objects.equals(StatusEnum.ACTIVE.getCode(), status)) {
            return;
        }
        PunchClassConfigSwitchStatusDTO switchStatusDTO = PunchClassConfigSwitchStatusDTO.builder()
                .classId(classId)
                .userIds(userIds)
                .status(status)
                .build();
        punchClassConfigEventPublisher.sendPunchClassConfigSwitchStatusEvent(switchStatusDTO);
    }

    @Override
    protected Boolean checkIsOccupiedOtherSchedulingTasks(Set<Long> userIds, OperationTypeEnum operationTypeEnum) {
        if (CollectionUtils.isEmpty(userIds) || OperationTypeEnum.PUNCH_CLASS_CONFIG_ADD.equals(operationTypeEnum)) {
            return Boolean.FALSE;
        }
        return shiftTaskService.checkUsersIsOccupiedByTask(new ArrayList<>(userIds));
    }

    @Override
    protected void classNatureSwitchPostProcessor(Long userId) {
        //多班次无需处理
    }

    @Override
    protected void classConfigRangeAddBuild(PunchClassConfigAddDTO addDTO,
                                            PunchClassConfigDO classConfigDO,
                                            List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList,
                                            List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList,
                                            DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        String remark = "页面新增绑定班次";
        if (addDTO.countryLevel()) {
            List<Long> countryUserIds = userInfoManage.selectByCountryAndClassNature(addDTO.getCountry(), addDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(countryUserIds)) {
                return;
            }
            build(countryUserIds, currentDateAndTimeZoneDate, RuleRangeTypeEnum.COUNTRY.getCode(), classConfigDO.getId(), classConfigDO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
            //特殊处理从HR迁移的历史规则
            List<PunchClassConfigRangeDO> hrHistoryRangeList = punchClassConfigRangeDao.selectLatestByBizIds(countryUserIds)
                    .stream()
                    .filter(range -> Objects.equals(RuleRangeTypeEnum.COUNTRY.getCode(), range.getRangeType())
                            && Objects.equals(BusinessConstant.Y, range.getIsFromHrHistoryConfig())
                            && Objects.equals(StatusEnum.DISABLED.getCode(), range.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hrHistoryRangeList)) {
                hrHistoryRangeList.forEach(range -> {
                    range.setIsLatest(BusinessConstant.N);
                    range.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                    range.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                });
                updatePunchClassConfigRangeDOList.addAll(hrHistoryRangeList);
            }

            return;
        }

        Set<Long> userIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(addDTO.getUserIdList())) {
            userIdList.addAll(addDTO.getUserIdList());
            build(addDTO.getUserIdList(), currentDateAndTimeZoneDate, RuleRangeTypeEnum.USER.getCode(), classConfigDO.getId(), classConfigDO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
            List<PunchClassConfigRangeDO> hrHistoryRangeList = punchClassConfigRangeDao.selectLatestByBizIds(addDTO.getUserIdList())
                    .stream()
                    .filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType())
                            && Objects.equals(BusinessConstant.Y, range.getIsFromHrHistoryConfig())
                            && Objects.equals(StatusEnum.DISABLED.getCode(), range.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hrHistoryRangeList)) {
                hrHistoryRangeList.forEach(range -> {
                    range.setIsLatest(BusinessConstant.N);
                    range.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                    range.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                });
                updatePunchClassConfigRangeDOList.addAll(hrHistoryRangeList);
            }
        }

        if (CollectionUtils.isEmpty(addDTO.getDeptIds())) {
            return;
        }

        List<Long> userIds = userInfoManage.selectByDeptIdsAndClassNature(addDTO.getCountry(), addDTO.getDeptIds(), addDTO.getClassNature())
                .stream().map(UserInfoDO::getId).filter(id -> !userIdList.contains(id)).collect(Collectors.toList());

        build(userIds, currentDateAndTimeZoneDate, RuleRangeTypeEnum.DEPT.getCode(), classConfigDO.getId(), classConfigDO.getConfigNo(), remark, addPunchClassConfigRangeDOList);

        List<PunchClassConfigRangeDO> hrHistoryRangeList = punchClassConfigRangeDao.selectLatestByBizIds(userIds)
                .stream()
                .filter(range -> Objects.equals(RuleRangeTypeEnum.DEPT.getCode(), range.getRangeType())
                        && Objects.equals(BusinessConstant.Y, range.getIsFromHrHistoryConfig())
                        && Objects.equals(StatusEnum.DISABLED.getCode(), range.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hrHistoryRangeList)) {
            hrHistoryRangeList.forEach(range -> {
                range.setIsLatest(BusinessConstant.N);
                range.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                range.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
            });
            updatePunchClassConfigRangeDOList.addAll(hrHistoryRangeList);
        }
    }


    @Override
    protected void classConfigRangeUpdateBuild(Boolean isClassInfoUpdate,
                                               DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                               Set<Long> removeUserIdList,
                                               PunchClassConfigDTO oldDto,
                                               PunchClassConfigAddDTO addDTO,
                                               PunchClassConfigDO classConfigDO,
                                               List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList,
                                               List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList) {
        String remark = "页面编辑绑定班次";
        //班次时段没变，不需要升级班次
        Long classId = oldDto.getId();
        String classConfigCode = oldDto.getConfigNo();
        if (isClassInfoUpdate) {
            classId = classConfigDO.getId();
        }

        Map<Long, PunchClassConfigRangeDTO> oldRangeDTOList = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(PunchClassConfigRangeDTO::getBizId, Function.identity()));

        if (CollectionUtils.isNotEmpty(removeUserIdList) && CollectionUtils.isNotEmpty(oldDto.getClassConfigRangeList())) {
            //移除的适用范围处理
            removeClassRangeBuild(currentDateAndTimeZoneDate, removeUserIdList, oldDto, updatePunchClassConfigRangeDOList);
        }

        if (addDTO.countryLevel()) {
            List<Long> countryUserIds = userInfoManage.selectByCountryAndClassNature(addDTO.getCountry(), addDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(countryUserIds)) {
                return;
            }

            build(countryUserIds, isClassInfoUpdate, currentDateAndTimeZoneDate, RuleRangeTypeEnum.COUNTRY.getCode(), classId, classConfigCode, remark, oldRangeDTOList,
                    addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);

            //特殊处理从HR迁移的历史规则
            List<PunchClassConfigRangeDO> hrHistoryRangeList = punchClassConfigRangeDao.selectLatestByBizIds(countryUserIds)
                    .stream()
                    .filter(range -> Objects.equals(RuleRangeTypeEnum.COUNTRY.getCode(), range.getRangeType())
                            && Objects.equals(BusinessConstant.Y, range.getIsFromHrHistoryConfig())
                            && Objects.equals(StatusEnum.DISABLED.getCode(), range.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hrHistoryRangeList)) {
                hrHistoryRangeList.forEach(range -> {
                    range.setIsLatest(BusinessConstant.N);
                    range.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                    range.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                });
                updatePunchClassConfigRangeDOList.addAll(hrHistoryRangeList);
            }
            return;
        }

        Set<Long> userIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(addDTO.getUserIdList())) {
            userIdList.addAll(addDTO.getUserIdList());
            build(addDTO.getUserIdList(), isClassInfoUpdate, currentDateAndTimeZoneDate, RuleRangeTypeEnum.USER.getCode(), classId, classConfigCode, remark, oldRangeDTOList,
                    addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);
            //特殊处理从HR迁移的历史规则
            List<PunchClassConfigRangeDO> hrHistoryRangeList = punchClassConfigRangeDao.selectLatestByBizIds(addDTO.getUserIdList())
                    .stream()
                    .filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType())
                            && Objects.equals(BusinessConstant.Y, range.getIsFromHrHistoryConfig())
                            && Objects.equals(StatusEnum.DISABLED.getCode(), range.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hrHistoryRangeList)) {
                hrHistoryRangeList.forEach(range -> {
                    range.setIsLatest(BusinessConstant.N);
                    range.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                    range.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                });
                updatePunchClassConfigRangeDOList.addAll(hrHistoryRangeList);
            }
        }

        if (CollectionUtils.isEmpty(addDTO.getDeptIds())) {
            return;
        }

        List<Long> userIds = userInfoManage.selectByDeptIdsAndClassNature(addDTO.getCountry(), addDTO.getDeptIds(), addDTO.getClassNature())
                .stream().map(UserInfoDO::getId).filter(id -> !userIdList.contains(id)).collect(Collectors.toList());
        build(userIds, isClassInfoUpdate, currentDateAndTimeZoneDate, RuleRangeTypeEnum.DEPT.getCode(), classId, classConfigCode, remark, oldRangeDTOList,
                addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);
        List<PunchClassConfigRangeDO> hrHistoryRangeList = punchClassConfigRangeDao.selectLatestByBizIds(userIds)
                .stream()
                .filter(range -> Objects.equals(RuleRangeTypeEnum.DEPT.getCode(), range.getRangeType())
                        && Objects.equals(BusinessConstant.Y, range.getIsFromHrHistoryConfig())
                        && Objects.equals(StatusEnum.DISABLED.getCode(), range.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hrHistoryRangeList)) {
            hrHistoryRangeList.forEach(range -> {
                range.setIsLatest(BusinessConstant.N);
                range.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                range.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
            });
            updatePunchClassConfigRangeDOList.addAll(hrHistoryRangeList);
        }
    }


    @Override
    protected PunchClassConfigRangeDifferDTO judgePunchClassConfigRangeUpdate(PunchClassConfigAddDTO newDto, PunchClassConfigDTO oldDto, Boolean isClassInfoUpdate) {
        PunchClassConfigRangeDifferDTO result = new PunchClassConfigRangeDifferDTO();

        Long preDayId = DateHelper.getPreviousDayId(getDayIdByCountryTimeZone(oldDto.getCountry()));
        List<Long> alreadyShiftUserIdList = userShiftConfigManage.getUserIdsByPunchClassIdAndAfterDayId(oldDto.getId(), preDayId);
        log.info("多班次已排班用户:{}", JSON.toJSONString(alreadyShiftUserIdList));

        //旧的班次范围是国家级别
        if (oldDto.countryLevel()) {
            return getOldCountryRangeDifferDTO(newDto, oldDto, isClassInfoUpdate, result, alreadyShiftUserIdList);
        }

        //旧的班次范围非国家级别
        if (newDto.countryLevel()) {
            return getNewCountryRangeDifferDTO(newDto, oldDto, result, alreadyShiftUserIdList);
        }

        //新旧班次都不为国家级级别
        return getClassConfigRangeDifferDTO(newDto, oldDto, isClassInfoUpdate, result, alreadyShiftUserIdList);
    }

    @Override
    protected Set<Long> getClassRangeUserList(String country, String classNature, List<Long> userIds, List<Long> deptIds) {
        Set<Long> userIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            userIdList.addAll(userIds);
        }

        if (CollectionUtils.isNotEmpty(deptIds)) {
            Set<Long> deptUserIds = userInfoManage.selectByDeptIdsAndClassNature(country, deptIds, classNature)
                    .stream().map(UserInfoDO::getId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(deptUserIds)) {
                userIdList.addAll(deptUserIds);
            }
            return userIdList;
        }

        if (CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(deptIds)) {
            Set<Long> countryUserIds = userInfoManage.selectByCountryAndClassNature(country, classNature).stream().map(UserInfoDO::getId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(countryUserIds)) {
                userIdList.addAll(countryUserIds);
            }
        }
        return userIdList;
    }

    @Override
    protected void buildClassEnableRangeAddData(DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                PunchClassConfigDTO classConfigDTO,
                                                List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList) {
        String remark = "页面启用绑定班次";
        //国家级
        if (classConfigDTO.countryLevel()) {
            Set<Long> countryRangeUserIds = userInfoManage.selectByCountryAndClassNature(classConfigDTO.getCountry(), classConfigDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toSet());
            build(new ArrayList<>(countryRangeUserIds), currentDateAndTimeZoneDate, RuleRangeTypeEnum.COUNTRY.getCode(), classConfigDTO.getId(), classConfigDTO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
            return;
        }

        if (CollectionUtils.isEmpty(classConfigDTO.getClassConfigRangeList())) {
            if (StringUtils.isBlank(classConfigDTO.getDeptIds())) {
                return;
            }
            Set<Long> deptRangeUserIds = userInfoManage.selectByDeptIdsAndClassNature(classConfigDTO.getCountry(), classConfigDTO.convertDeptList(), classConfigDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toSet());
            build(new ArrayList<>(deptRangeUserIds), currentDateAndTimeZoneDate, RuleRangeTypeEnum.DEPT.getCode(), classConfigDTO.getId(), classConfigDTO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
            return;
        }

        Set<Long> userRangeSet = classConfigDTO.getClassConfigRangeList()
                .stream().filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                .map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());
        build(new ArrayList<>(userRangeSet), currentDateAndTimeZoneDate, RuleRangeTypeEnum.USER.getCode(), classConfigDTO.getId(), classConfigDTO.getConfigNo(), remark, addPunchClassConfigRangeDOList);

        if (CollectionUtils.isNotEmpty(classConfigDTO.convertDeptList())) {
            Set<Long> deptRangeUserIds = userInfoManage.selectByDeptIdsAndClassNature(classConfigDTO.getCountry(), classConfigDTO.convertDeptList(), classConfigDTO.getClassNature())
                    .stream().map(UserInfoDO::getId).filter(userId -> !userRangeSet.contains(userId)).collect(Collectors.toSet());
            build(new ArrayList<>(deptRangeUserIds), currentDateAndTimeZoneDate, RuleRangeTypeEnum.DEPT.getCode(), classConfigDTO.getId(), classConfigDTO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
        }
    }

    private void buildClassRangeDifferCount(PunchClassConfigDTO oldDto,
                                            PunchClassConfigAddDTO newDto,
                                            PunchClassConfigUpdateConfirmVO result) {
        boolean isRangeUpdate = false;
        List<Long> oldDeptIdList = StringUtils.isEmpty(oldDto.getDeptIds()) ? Collections.emptyList() : oldDto.convertDeptList();
        List<Long> newDeptIdList = Optional.ofNullable(newDto.getDeptIds()).orElse(Collections.emptyList());

        //移除的部门
        List<Long> removedDeptIdList = new ArrayList<>(oldDeptIdList);
        removedDeptIdList.removeAll(newDeptIdList);

        //新增的部门
        List<Long> addDeptIdList = new ArrayList<>(newDeptIdList);
        addDeptIdList.removeAll(oldDeptIdList);

        Set<Long> totalRemoveUserIdList = new HashSet<>();

        if (!removedDeptIdList.isEmpty() || !addDeptIdList.isEmpty()) {
            isRangeUpdate = true;
        }

        if (CollectionUtils.isNotEmpty(removedDeptIdList)) {
            List<Long> userIdList = userInfoManage.selectByDeptIdsAndClassNature(newDto.getCountry(), removedDeptIdList, newDto.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toList());
            totalRemoveUserIdList.addAll(userIdList);
        }

        List<Long> oldUserIdList = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                .map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toList());
        List<Long> newUserIdList = Optional.ofNullable(newDto.getUserIdList()).orElse(Collections.emptyList());

        //移除的用户
        List<Long> removedUserIdList = new ArrayList<>(oldUserIdList);
        removedUserIdList.removeAll(newUserIdList);

        totalRemoveUserIdList.addAll(removedUserIdList);

        //新增的部门
        List<Long> addUserIdList = new ArrayList<>(newUserIdList);
        addUserIdList.removeAll(oldUserIdList);

        if (!removedUserIdList.isEmpty() || !addUserIdList.isEmpty()) {
            isRangeUpdate = true;
        }

        if (CollectionUtils.isNotEmpty(totalRemoveUserIdList)) {
            result.setRemoveRangeEmployeeCount(totalRemoveUserIdList.size());
        }
        result.setRangeUpdate(isRangeUpdate);
    }

    private PunchClassConfigRangeDifferDTO getOldCountryRangeDifferDTO(PunchClassConfigAddDTO newDto,
                                                                       PunchClassConfigDTO oldDto,
                                                                       Boolean isClassInfoUpdate,
                                                                       PunchClassConfigRangeDifferDTO result,
                                                                       List<Long> alreadyShiftUserIdList) {
        Set<Long> newRangeUserList = getClassRangeUserList(newDto.getCountry(), newDto.getClassNature(), newDto.getUserIdList(), newDto.getDeptIds());

        if (!newDto.countryLevel()) {
            //旧的班次国家适用范围人数
            Set<Long> oldCountryRangeUserIds = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                    .stream().map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());

            //编辑前后不变的适用范围人群需要更新排班的信息
            if (CollectionUtils.isNotEmpty(newRangeUserList) && CollectionUtils.isNotEmpty(alreadyShiftUserIdList)) {
                List<Long> mixUserIdList = new ArrayList<>(newRangeUserList);
                mixUserIdList.retainAll(alreadyShiftUserIdList);
                if (CollectionUtils.isNotEmpty(mixUserIdList)) {
                    result.setNoChangeRangeUserList(new HashSet<>(mixUserIdList));
                }
            }

            //需要清理排班的适用范围用户
            oldCountryRangeUserIds.removeAll(newRangeUserList);
            if (CollectionUtils.isNotEmpty(oldCountryRangeUserIds)) {
                result.setRemoveRangeUserList(oldCountryRangeUserIds);
            }
            result.setRangeUpdate(Boolean.TRUE);
        } else {
            if (isClassInfoUpdate && CollectionUtils.isNotEmpty(alreadyShiftUserIdList)) {
                result.setNoChangeRangeUserList(new HashSet<>(alreadyShiftUserIdList));
            }
        }
        return result;
    }

    @NotNull
    private PunchClassConfigRangeDifferDTO getNewCountryRangeDifferDTO(PunchClassConfigAddDTO newDto,
                                                                       PunchClassConfigDTO oldDto,
                                                                       PunchClassConfigRangeDifferDTO result,
                                                                       List<Long> alreadyShiftUserIdList) {
        Set<Long> oldUserIdSet = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toSet());

        //新的规则适用范围人数
        Set<Long> countryRangeUserIds = userInfoManage.selectByCountryAndClassNature(newDto.getCountry(), newDto.getClassNature())
                .stream().map(UserInfoDO::getId).collect(Collectors.toSet());

        Collection<Long> removeUserIdList = CollectionUtils.subtract(oldUserIdSet, countryRangeUserIds);
        if (CollectionUtils.isNotEmpty(removeUserIdList)) {
            result.setRemoveRangeUserList(new HashSet<>(removeUserIdList));
        }

        //编辑前后不变的适用范围人群需要更新排班的信息
        countryRangeUserIds.retainAll(oldUserIdSet);
        if (CollectionUtils.isNotEmpty(countryRangeUserIds) && CollectionUtils.isNotEmpty(alreadyShiftUserIdList)) {
            countryRangeUserIds.retainAll(alreadyShiftUserIdList);
            result.setNoChangeRangeUserList(countryRangeUserIds);
        }
        result.setRangeUpdate(Boolean.TRUE);
        return result;
    }

    private PunchClassConfigRangeDifferDTO getClassConfigRangeDifferDTO(PunchClassConfigAddDTO newDto,
                                                                        PunchClassConfigDTO oldDto,
                                                                        Boolean isClassInfoUpdate,
                                                                        PunchClassConfigRangeDifferDTO result,
                                                                        List<Long> alreadyShiftUserIdList) {
        boolean isRangeUpdate = false;
        List<Long> oldDeptIdList = StringUtils.isEmpty(oldDto.getDeptIds()) ? Collections.emptyList() : oldDto.convertDeptList();
        List<Long> newDeptIdList = Optional.ofNullable(newDto.getDeptIds()).orElse(Collections.emptyList());

        //移除的部门
        List<Long> removedDeptIdList = new ArrayList<>(oldDeptIdList);
        removedDeptIdList.removeAll(newDeptIdList);

        //新增的部门
        List<Long> addDeptIdList = new ArrayList<>(newDeptIdList);
        addDeptIdList.removeAll(oldDeptIdList);

        //不变的部门
        List<Long> mixDeptIdList = new ArrayList<>(oldDeptIdList);
        mixDeptIdList.retainAll(newDeptIdList);

        Set<Long> totalRemoveUserIdList = new HashSet<>();
        Set<Long> totalMixUserIdList = new HashSet<>();

        if (!removedDeptIdList.isEmpty() || !addDeptIdList.isEmpty()) {
            isRangeUpdate = true;
        }

        if (CollectionUtils.isNotEmpty(removedDeptIdList)) {
            List<Long> userIdList = userInfoManage.selectByDeptIdsAndClassNature(newDto.getCountry(), removedDeptIdList, newDto.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toList());
            totalRemoveUserIdList.addAll(userIdList);
        }

        if (CollectionUtils.isNotEmpty(mixDeptIdList)) {
            List<Long> userIdList = userInfoManage.selectByDeptIdsAndClassNature(newDto.getCountry(), mixDeptIdList, newDto.getClassNature())
                    .stream().map(UserInfoDO::getId).collect(Collectors.toList());
            totalMixUserIdList.addAll(userIdList);
        }

        List<Long> oldUserIdList = Optional.ofNullable(oldDto.getClassConfigRangeList()).orElse(Collections.emptyList())
                .stream().filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                .map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toList());
        List<Long> newUserIdList = Optional.ofNullable(newDto.getUserIdList()).orElse(Collections.emptyList());

        //移除的用户
        List<Long> removedUserIdList = new ArrayList<>(oldUserIdList);
        removedUserIdList.removeAll(newUserIdList);

        //新增的用户
        List<Long> addUserIdList = new ArrayList<>(newUserIdList);
        addUserIdList.removeAll(oldUserIdList);

        //不变的用户
        List<Long> mixUserIdList = new ArrayList<>(oldUserIdList);
        mixUserIdList.retainAll(newUserIdList);

        if (!removedUserIdList.isEmpty() || !addUserIdList.isEmpty()) {
            isRangeUpdate = true;
        }

        totalRemoveUserIdList.addAll(removedUserIdList);
        totalMixUserIdList.addAll(mixUserIdList);

        Set<Long> rangeUserList = getClassRangeUserList(newDto.getCountry(), newDto.getClassNature(), newDto.getUserIdList(), newDto.getDeptIds());
        if (isRangeUpdate) {
            result.setRangeUpdate(isRangeUpdate);
            if (CollectionUtils.isNotEmpty(totalRemoveUserIdList)) {
                result.setRemoveRangeUserList(totalRemoveUserIdList);
            }
            if (CollectionUtils.isNotEmpty(alreadyShiftUserIdList) && CollectionUtils.isNotEmpty(totalMixUserIdList)) {
                totalMixUserIdList.retainAll(alreadyShiftUserIdList);
                result.setNoChangeRangeUserList(totalMixUserIdList);
            }
        } else {
            if (isClassInfoUpdate && CollectionUtils.isNotEmpty(alreadyShiftUserIdList)) {
                rangeUserList.retainAll(alreadyShiftUserIdList);
                result.setNoChangeRangeUserList(rangeUserList);
            }
        }
        return result;
    }


}
