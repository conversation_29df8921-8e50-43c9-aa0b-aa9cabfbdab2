package com.imile.attendance.migration.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendanceClassEmployeeConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigHistoryDao;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigHistoryDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.migration.UserShiftConfigMigrationService;
import com.imile.attendance.migration.dto.BatchUpdateShiftTypeResult;
import com.imile.attendance.migration.processor.UserShiftConfigMigrationProcessor;
import com.imile.attendance.infrastructure.repository.employee.dto.UserEntryInfoDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 排班数据迁移服务
 * 负责协调整个迁移流程
 *
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Slf4j
@Service
public class UserShiftConfigMigrationServiceImpl implements UserShiftConfigMigrationService {

    @Resource
    private UserShiftConfigMigrationProcessor migrationProcessor;
    @Resource
    private MigrationService migrationService;
    @Resource
    private HrmsAttendanceClassEmployeeConfigDao hrmsClassEmployeeConfigDao;
    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private UserShiftConfigHistoryDao userShiftConfigHistoryDao;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private AttendanceProperties attendanceProperties;


    @Override
    @DSTransactional
    public Boolean migrateByCountryNoGrayscaleUser(String country, Long shiftStartDayId) {
        log.info("开始为非灰度用户迁移排班数据, country: {}, shiftStartDayId: {}", country, shiftStartDayId);
        XxlJobLogger.log("开始为非灰度用户迁移排班数据, country: {}, shiftStartDayId: {}", country, shiftStartDayId);

        try {
            // 1. 获取非灰度用户ID
            List<Long> nonGrayScaleUserIds = getNonGrayScaleUserIds(country);
            if (CollectionUtils.isEmpty(nonGrayScaleUserIds)) {
                log.info("未找到需要迁移的非灰度用户, country: {}", country);
                XxlJobLogger.log("未找到需要迁移的非灰度用户, country: {}", country);
                return true;
            }
            log.info("找到 {} 个非灰度用户进行排班迁移", nonGrayScaleUserIds.size());
            XxlJobLogger.log("找到 {} 个非灰度用户进行排班迁移", nonGrayScaleUserIds.size());

            // 3. 为这些用户迁移新的排班数据
            Boolean migrationResult = migrateByUserIdsAndDateRange(nonGrayScaleUserIds, shiftStartDayId, null);

            if (Boolean.TRUE.equals(migrationResult)) {
                log.info("成功为 {} 个非灰度用户迁移了排班数据", nonGrayScaleUserIds.size());
                XxlJobLogger.log("成功为 {} 个非灰度用户迁移了排班数据", nonGrayScaleUserIds.size());
            } else {
                log.error("为非灰度用户迁移排班数据失败, country: {}", country);
                XxlJobLogger.log("为非灰度用户迁移排班数据失败, country: {}", country);
                // 注意：这里需要根据业务决定是否手动回滚或采取其他措施
            }

            return migrationResult;

        } catch (Exception e) {
            log.error("为非灰度用户迁移排班数据时发生异常, country: {}", country, e);
            XxlJobLogger.log("为非灰度用户迁移排班数据时发生异常, country: {}, error: {}", country, e.getMessage());
            return false;
        }
    }

    @Override
    @DSTransactional
    public Boolean rollbackCurrentDataOnly(String country, Long startDayId, Long endDayId) {
        log.info("开始执行当前数据迁移回滚, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);
        XxlJobLogger.log("开始执行当前数据迁移回滚, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);

        try {
            // 验证参数
            if (StringUtils.isBlank(country)) {
                log.error("回滚操作失败: 国家参数不能为空");
                XxlJobLogger.log("回滚操作失败: 国家参数不能为空");
                throw new IllegalArgumentException("国家参数不能为空");
            }

            // 执行回滚操作
            Boolean result = migrationProcessor.rollbackCurrentData(country, startDayId, endDayId);

            if (Boolean.TRUE.equals(result)) {
                log.info("当前数据迁移回滚完成, country: {}", country);
                XxlJobLogger.log("当前数据迁移回滚完成, country: {}", country);
            } else {
                log.error("当前数据迁移回滚失败, country: {}", country);
                XxlJobLogger.log("当前数据迁移回滚失败, country: {}", country);
            }

            return result;

        } catch (Exception e) {
            log.error("当前数据迁移回滚失败, country: {}", country, e);
            XxlJobLogger.log("当前数据迁移回滚失败, country: {}, error: {}", country, e.getMessage());
            return false;
        }
    }

    /**
     * 获取指定国家的非灰度用户ID列表
     */
    private List<Long> getNonGrayScaleUserIds(String country) {
        // 1. 获取国家下的所有相关用户
        UserDaoQuery query = UserDaoQuery.builder()
                .locationCountry(country)
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .build();
        List<UserInfoDO> allUsers = userInfoDao.userList(query);
        if (CollectionUtils.isEmpty(allUsers)) {
            return new ArrayList<>();
        }

        // 2. 获取所有灰度用户
        AttendanceProperties.Attendance attendance = attendanceProperties.getAttendance();

        List<String> grayscaleDeptList = Arrays.stream(attendance.getGrayscaleDept()
                .split(",")).collect(Collectors.toList());
        Set<String> grayUserCodeSet = Arrays.stream(attendance.getGrayscaleUserCode()
                .split(",")).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(grayscaleDeptList)) {
            List<Long> deptIds = grayscaleDeptList.stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            // 2.1 获取灰度部门下的所有用户（添加指定的国家条件）
            UserDaoQuery grayDeptUserDaoQuery = UserDaoQuery.builder()
                    .locationCountry(country)
                    .deptIds(deptIds)
                    .build();
            List<UserInfoDO> grayDeptUsers = userInfoDao.userList(grayDeptUserDaoQuery);
            if (CollectionUtils.isNotEmpty(grayDeptUsers)) {
                grayDeptUsers.forEach(user -> grayUserCodeSet.add(user.getUserCode()));
            }
        }

        // 3. 过滤出非灰度用户
        return allUsers.stream()
                .filter(user -> !grayUserCodeSet.contains(user.getUserCode()))
                .map(UserInfoDO::getId)
                .collect(Collectors.toList());
    }

    /**
     * 删除指定用户的排班数据（物理删除）
     *
     * @param userIds         用户ID列表
     * @param shiftStartDayId 排班开始日期ID
     */
    private void deleteShiftDataByUserIds(List<Long> userIds, Long shiftStartDayId) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        final int BATCH_SIZE = 1000;
        int totalProcessed = 0;

        log.debug("Starting batch deletion for {} users", userIds.size());
        XxlJobLogger.log("Starting batch deletion for {} users", userIds.size());

        // 分批查询和更新用户配置
        for (int i = 0; i < userIds.size(); i += BATCH_SIZE) {
            List<Long> currentBatch = userIds.subList(i, Math.min(i + BATCH_SIZE, userIds.size()));

            // 1. 分批查询当前批次的记录
            List<UserShiftConfigDO> configRecords = queryBatchByUserIds(currentBatch, shiftStartDayId);
            // 2. 处理查询结果
            int totalRecords = configRecords != null ? configRecords.size() : 0;

            if (totalRecords > 0) {
                // 3. 分批删除记录
                batchDeleteUserShiftRecord(configRecords);
                totalProcessed += totalRecords;
            }

            // 4. 进度报告（每5000用户打印一次）
            if ((i / BATCH_SIZE) % 5 == 0) {
                log.debug("批次进度：已处理 {}/{} 用户，共 {} 条记录",
                        Math.min(i + BATCH_SIZE, userIds.size()), userIds.size(), totalProcessed);
                XxlJobLogger.log("批次进度：已处理 {}/{} 用户，共 {} 条记录",
                        Math.min(i + BATCH_SIZE, userIds.size()), userIds.size(), totalProcessed);
            }
        }

        log.info("完成删除处理：共处理 {} 用户，涉及 {} 条记录", userIds.size(), totalProcessed);
        XxlJobLogger.log("完成删除处理：共处理 {} 用户，涉及 {} 条记录", userIds.size(), totalProcessed);
    }

    /**
     * 分批查询用户当前表记录
     */
    private List<UserShiftConfigDO> queryBatchByUserIds(List<Long> userBatch, Long startDayId) {
        if (CollectionUtils.isEmpty(userBatch)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserShiftConfigDO::getUserId, userBatch);

        if (startDayId != null) {
            queryWrapper.ge(UserShiftConfigDO::getDayId, startDayId);
        }

        return userShiftConfigDao.list(queryWrapper);
    }

    /**
     * 批量删除用户历史排班记录
     */
    private void batchDeleteUserShiftRecord(List<UserShiftConfigDO> userShiftConfigList) {
        final int UPDATE_BATCH_SIZE = 500;
        if (CollectionUtils.isNotEmpty(userShiftConfigList)) {
            List<List<UserShiftConfigDO>> currentBatches = Lists.partition(userShiftConfigList, UPDATE_BATCH_SIZE);
            for (List<UserShiftConfigDO> currentBatch : currentBatches) {
                for (UserShiftConfigDO userShiftConfigDO : currentBatch) {
                    BaseDOUtil.fillDOUpdateByUserOrSystem(userShiftConfigDO);
                    userShiftConfigDO.setIsDelete(BusinessConstant.Y);
                }
                userShiftConfigDao.removeByIds(currentBatch);
            }
        }
    }


    /**
     * 执行完整的数据迁移
     *
     * @param country 国家编码
     * @return 迁移结果
     */
    @DSTransactional
    public Boolean executeFullMigration(String country) {
        log.info("开始执行排班数据完整迁移, country: {}", country);
        XxlJobLogger.log("开始执行排班数据完整迁移, country: {}", country);

        try {
            // 验证参数
            if (StringUtils.isBlank(country)) {
                log.error("国家参数不能为空");
                XxlJobLogger.log("国家参数不能为空");
                return false;
            }

            // 阶段1：迁移历史数据（2024年及之前）
            XxlJobLogger.log("阶段1：开始迁移历史数据（2024年及之前）");
            Boolean historyResult = migrationProcessor.migrateHistoryData(
                    country, null, BusinessConstant.ShiftMigration.HISTORY_END_DAY_ID);

            if (!Boolean.TRUE.equals(historyResult)) {
                log.error("历史数据迁移失败, country: {}", country);
                XxlJobLogger.log("历史数据迁移失败, country: {}", country);
                return false;
            }

            // 阶段2：迁移当前数据（2025年及之后）
            XxlJobLogger.log("阶段2：开始迁移当前数据（2025年及之后）");
            Boolean currentResult = migrationProcessor.migrateCurrentData(
                    country, BusinessConstant.ShiftMigration.SPLIT_DAY_ID, null);

            if (!Boolean.TRUE.equals(currentResult)) {
                log.error("当前数据迁移失败, country: {}", country);
                XxlJobLogger.log("当前数据迁移失败, country: {}", country);
                return false;
            }

            return true;

//            // 阶段3：数据验证
//            XxlJobLogger.log("阶段3：开始数据验证");
//            Boolean validationResult = validateMigrationResult(country);
//
//            if (Boolean.TRUE.equals(validationResult)) {
//                log.info("排班数据迁移完成, country: {}", country);
//                XxlJobLogger.log("排班数据迁移完成, country: {}", country);
//                return true;
//            } else {
//                log.error("排班数据迁移验证失败, country: {}", country);
//                XxlJobLogger.log("排班数据迁移验证失败, country: {}", country);
//                return false;
//            }

        } catch (Exception e) {
            log.error("排班数据迁移失败, country: {}", country, e);
            XxlJobLogger.log("排班数据迁移失败, country: {}, error: {}", country, e.getMessage());
            return false;
        }
    }

    /**
     * 仅迁移历史数据
     *
     * @param country    国家代码
     * @param startDayId 开始日期ID
     * @param endDayId   结束日期ID
     * @return 迁移结果
     */
    @DSTransactional
    public Boolean migrateHistoryDataOnly(String country, Long startDayId, Long endDayId) {
        log.info("开始执行历史数据迁移, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);
        XxlJobLogger.log("开始执行历史数据迁移, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);

        try {
            // 验证参数
            if (StringUtils.isBlank(country)) {
                log.error("国家参数不能为空");
                XxlJobLogger.log("国家参数不能为空");
                return false;
            }

            // 执行历史数据迁移
            Boolean result = migrationProcessor.migrateHistoryData(country, startDayId, endDayId);

            if (Boolean.TRUE.equals(result)) {
                log.info("历史数据迁移完成, country: {}", country);
                XxlJobLogger.log("历史数据迁移完成, country: {}", country);
            } else {
                log.error("历史数据迁移失败, country: {}", country);
                XxlJobLogger.log("历史数据迁移失败, country: {}", country);
            }

            return result;

        } catch (Exception e) {
            log.error("历史数据迁移失败, country: {}", country, e);
            XxlJobLogger.log("历史数据迁移失败, country: {}, error: {}", country, e.getMessage());
            return false;
        }
    }

    /**
     * 仅迁移当前数据
     *
     * @param country    国家代码
     * @param startDayId 开始日期ID
     * @param endDayId   结束日期ID
     * @return 迁移结果
     */
    @DSTransactional
    public Boolean migrateCurrentDataOnly(String country, Long startDayId, Long endDayId) {
        log.info("开始执行当前数据迁移, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);
        XxlJobLogger.log("开始执行当前数据迁移, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);

        try {
            // 验证参数
            if (StringUtils.isBlank(country)) {
                log.error("国家参数不能为空");
                XxlJobLogger.log("国家参数不能为空");
                return false;
            }

            // 执行当前数据迁移
            Boolean result = migrationProcessor.migrateCurrentData(country, startDayId, endDayId);

            if (Boolean.TRUE.equals(result)) {
                log.info("当前数据迁移完成, country: {}", country);
                XxlJobLogger.log("当前数据迁移完成, country: {}", country);
            } else {
                log.error("当前数据迁移失败, country: {}", country);
                XxlJobLogger.log("当前数据迁移失败, country: {}", country);
            }

            return result;

        } catch (Exception e) {
            log.error("当前数据迁移失败, country: {}", country, e);
            XxlJobLogger.log("当前数据迁移失败, country: {}, error: {}", country, e.getMessage());
            return false;
        }
    }

    /**
     * 验证迁移结果
     *
     * @param country 国家代码
     * @return 验证结果
     */
    private Boolean validateMigrationResult(String country) {
        try {
            log.info("开始验证迁移结果, country: {}", country);
            XxlJobLogger.log("开始验证迁移结果, country: {}", country);

            // 验证历史数据
            Long historySourceCount = hrmsClassEmployeeConfigDao.countByCountryAndDateRange(
                    country, null, BusinessConstant.ShiftMigration.HISTORY_END_DAY_ID);
            Long historyTargetCount = countHistoryDataByCountry(country);

            log.info("历史数据验证, country: {}, 源数据: {}, 目标数据: {}",
                    country, historySourceCount, historyTargetCount);
            XxlJobLogger.log("历史数据验证, country: {}, 源数据: {}, 目标数据: {}",
                    country, historySourceCount, historyTargetCount);

            // 验证当前数据
            Long currentSourceCount = hrmsClassEmployeeConfigDao.countByCountryAndDateRange(
                    country, BusinessConstant.ShiftMigration.SPLIT_DAY_ID, null);
            Long currentTargetCount = countCurrentDataByCountry(country);

            log.info("当前数据验证, country: {}, 源数据: {}, 目标数据: {}",
                    country, currentSourceCount, currentTargetCount);
            XxlJobLogger.log("当前数据验证, country: {}, 源数据: {}, 目标数据: {}",
                    country, currentSourceCount, currentTargetCount);

            // 简单的数量验证（实际项目中可能需要更复杂的验证逻辑）
            boolean historyValid = historySourceCount.equals(historyTargetCount);
            boolean currentValid = currentSourceCount.equals(currentTargetCount);

            if (historyValid && currentValid) {
                log.info("数据验证通过, country: {}", country);
                XxlJobLogger.log("数据验证通过, country: {}", country);
                return true;
            } else {
                log.error("数据验证失败, country: {}, 历史数据验证: {}, 当前数据验证: {}",
                        country, historyValid, currentValid);
                XxlJobLogger.log("数据验证失败, country: {}, 历史数据验证: {}, 当前数据验证: {}",
                        country, historyValid, currentValid);
                return false;
            }

        } catch (Exception e) {
            log.error("数据验证失败, country: {}", country, e);
            XxlJobLogger.log("数据验证失败, country: {}, error: {}", country, e.getMessage());
            return false;
        }
    }

    /**
     * 统计历史表中指定国家的数据量
     * 注意：这里需要关联用户表来按国家统计，实际实现可能需要在DAO中添加相应方法
     */
    private Long countHistoryDataByCountry(String country) {
        // TODO: 实现按国家统计历史表数据的逻辑
        return 0L;
    }

    /**
     * 统计新表中指定国家的数据量
     * 注意：这里需要关联用户表来按国家统计，实际实现可能需要在DAO中添加相应方法
     */
    private Long countCurrentDataByCountry(String country) {
        // TODO: 实现按国家统计新表数据的逻辑
        return 0L;
    }

    /**
     * 批量更新HRMS迁移排班数据的排班类型
     *
     * @return 批量更新结果，包含详细的处理统计信息
     */
    @Override
    @DSTransactional
    public BatchUpdateShiftTypeResult batchUpdateShiftTypeForHrmsMigration() {
        // 设置固定批次大小为1000条记录
        final Integer batchSize = 1000;

        log.info("开始批量更新HRMS迁移排班数据的排班类型，自动分页批次大小: {}", batchSize);
        XxlJobLogger.log("开始批量更新HRMS迁移排班数据的排班类型，自动分页批次大小: {}", batchSize);

        BatchUpdateShiftTypeResult result = null;

        try {
            // 第一步：统计需要更新的总记录数
            Integer normalTableCount = countNormalTableRecords();
            Integer historyTableCount = countHistoryTableRecords();
            Integer totalCount = normalTableCount + historyTableCount;

            log.info("统计完成 - 正常表: {}, 历史表: {}, 总计: {}", normalTableCount, historyTableCount, totalCount);
            XxlJobLogger.log("统计完成 - 正常表: {}, 历史表: {}, 总计: {}", normalTableCount, historyTableCount, totalCount);

            if (totalCount == 0) {
                log.info("没有需要更新的记录");
                XxlJobLogger.log("没有需要更新的记录");
                result = BatchUpdateShiftTypeResult.createInitial(0, batchSize);
                result.markCompleted();
                return result;
            }

            // 创建初始结果
            result = BatchUpdateShiftTypeResult.createInitial(totalCount, batchSize);

            // 第二步：分批处理正常排班表
            Integer normalProcessed = processBatchUpdateNormalTable(batchSize, result, normalTableCount);

            // 第三步：分批处理历史排班表
            Integer historyProcessed = processBatchUpdateHistoryTable(batchSize, result, historyTableCount, normalProcessed);

            // 标记完成
            result.markCompleted();

            log.info("批量更新HRMS迁移排班类型完成（自动分页版本） - 总计: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailedCount(), result.getDuration());
            XxlJobLogger.log("批量更新HRMS迁移排班类型完成（自动分页版本） - 总计: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailedCount(), result.getDuration());

            return result;

        } catch (Exception e) {
            log.error("批量更新HRMS迁移排班类型失败（自动分页版本）", e);
            XxlJobLogger.log("批量更新HRMS迁移排班类型失败（自动分页版本）, error: {}", e.getMessage());

            if (result != null) {
                result.markFailed(e.getMessage());
            } else {
                result = new BatchUpdateShiftTypeResult();
                result.markFailed(e.getMessage());
            }
            return result;
        }
    }

    /**
     * 统计正常排班表中需要更新的记录数
     */
    private Integer countNormalTableRecords() {
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.likeRight(UserShiftConfigDO::getTaskFlag, BusinessConstant.ShiftMigration.MIGRATE_FROM_HRMS);
        return userShiftConfigDao.count(queryWrapper);
    }

    /**
     * 统计历史排班表中需要更新的记录数
     */
    private Integer countHistoryTableRecords() {
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.likeRight(UserShiftConfigHistoryDO::getTaskFlag, BusinessConstant.ShiftMigration.MIGRATE_FROM_HRMS);
        return userShiftConfigHistoryDao.count(queryWrapper);
    }

    /**
     * 分批处理正常排班表更新
     */
    private Integer processBatchUpdateNormalTable(Integer batchSize, BatchUpdateShiftTypeResult result, Integer totalCount) {
        log.info("开始分批处理正常排班表，总记录数: {}", totalCount);
        XxlJobLogger.log("开始分批处理正常排班表，总记录数: {}", totalCount);

        Integer processedCount = 0;
        Integer successCount = 0;
        Integer failedCount = 0;
        Integer currentBatch = 0;

        while (processedCount < totalCount) {
            currentBatch++;
            long batchStartTime = System.currentTimeMillis();

            try {
                // 查询当前批次的ID列表
                List<Long> batchIds = getNormalTableBatchIds(batchSize);

                if (batchIds.isEmpty()) {
                    log.info("正常排班表没有更多记录需要处理");
                    break;
                }

                // 批量更新当前批次
                boolean batchResult = updateNormalTableBatch(batchIds);

                if (batchResult) {
                    successCount += batchIds.size();
                } else {
                    failedCount += batchIds.size();
                }

                processedCount += batchIds.size();

                long batchEndTime = System.currentTimeMillis();
                log.info("正常排班表批次 {} 处理完成，记录数: {}, 结果: {}, 耗时: {}ms",
                        currentBatch, batchIds.size(), batchResult ? "成功" : "失败", batchEndTime - batchStartTime);

                // 更新总体进度
                result.updateProgress(processedCount, successCount, failedCount, currentBatch);

                // 避免过于频繁的处理，稍作休息
                if (currentBatch % 10 == 0) {
                    Thread.sleep(100);
                }

            } catch (Exception e) {
                log.error("正常排班表批次 {} 处理失败", currentBatch, e);
                failedCount += batchSize;
                processedCount += batchSize;
            }
        }

        log.info("正常排班表处理完成，总处理: {}, 成功: {}, 失败: {}", processedCount, successCount, failedCount);
        XxlJobLogger.log("正常排班表处理完成，总处理: {}, 成功: {}, 失败: {}", processedCount, successCount, failedCount);

        return processedCount;
    }

    /**
     * 分批处理历史排班表更新
     */
    private Integer processBatchUpdateHistoryTable(Integer batchSize,
                                                   BatchUpdateShiftTypeResult result,
                                                   Integer totalCount,
                                                   Integer previousProcessed) {
        log.info("开始分批处理历史排班表，总记录数: {}", totalCount);
        XxlJobLogger.log("开始分批处理历史排班表，总记录数: {}", totalCount);

        Integer processedCount = 0;
        Integer successCount = result.getSuccessCount();
        Integer failedCount = result.getFailedCount();
        int currentBatch = result.getCompletedBatches();

        while (processedCount < totalCount) {
            currentBatch++;
            long batchStartTime = System.currentTimeMillis();

            try {
                // 查询当前批次的ID列表
                List<Long> batchIds = getHistoryTableBatchIds(batchSize);

                if (batchIds.isEmpty()) {
                    log.info("历史排班表没有更多记录需要处理");
                    break;
                }

                // 批量更新当前批次
                boolean batchResult = updateHistoryTableBatch(batchIds);

                if (batchResult) {
                    successCount += batchIds.size();
                } else {
                    failedCount += batchIds.size();
                }

                processedCount += batchIds.size();

                long batchEndTime = System.currentTimeMillis();
                log.info("历史排班表批次 {} 处理完成，记录数: {}, 结果: {}, 耗时: {}ms",
                        currentBatch, batchIds.size(), batchResult ? "成功" : "失败", batchEndTime - batchStartTime);

                // 更新总体进度
                result.updateProgress(previousProcessed + processedCount, successCount, failedCount, currentBatch);

                // 避免过于频繁的处理，稍作休息
                if (currentBatch % 10 == 0) {
                    Thread.sleep(100);
                }

            } catch (Exception e) {
                log.error("历史排班表批次 {} 处理失败", currentBatch, e);
                failedCount += batchSize;
                processedCount += batchSize;
            }
        }

        log.info("历史排班表处理完成，总处理: {}, 成功: {}, 失败: {}", processedCount, successCount, failedCount);
        XxlJobLogger.log("历史排班表处理完成，总处理: {}, 成功: {}, 失败: {}", processedCount, successCount, failedCount);

        return processedCount;
    }

    /**
     * 获取正常排班表批次ID列表
     */
    private List<Long> getNormalTableBatchIds(Integer batchSize) {
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(UserShiftConfigDO::getId)
                .likeRight(UserShiftConfigDO::getTaskFlag, BusinessConstant.ShiftMigration.MIGRATE_FROM_HRMS)
                .ne(UserShiftConfigDO::getShiftType, ShiftTypeEnum.MIGRATE_FROM_HRMS.getCode())
                .last("LIMIT " + batchSize);

        List<UserShiftConfigDO> records = userShiftConfigDao.list(queryWrapper);
        return records.stream()
                .map(UserShiftConfigDO::getId)
                .collect(Collectors.toList());
    }

    /**
     * 获取历史排班表批次ID列表
     */
    private List<Long> getHistoryTableBatchIds(Integer batchSize) {
        LambdaQueryWrapper<UserShiftConfigHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(UserShiftConfigHistoryDO::getId)
                .likeRight(UserShiftConfigHistoryDO::getTaskFlag, BusinessConstant.ShiftMigration.MIGRATE_FROM_HRMS)
                .ne(UserShiftConfigHistoryDO::getShiftType, ShiftTypeEnum.MIGRATE_FROM_HRMS.getCode())
                .last("LIMIT " + batchSize);

        List<UserShiftConfigHistoryDO> records = userShiftConfigHistoryDao.list(queryWrapper);
        return records.stream()
                .map(UserShiftConfigHistoryDO::getId)
                .collect(Collectors.toList());
    }

    /**
     * 批量更新正常排班表指定ID的记录
     */
    public boolean updateNormalTableBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }

        try {
            LambdaUpdateWrapper<UserShiftConfigDO> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.in(UserShiftConfigDO::getId, ids)
                    .set(UserShiftConfigDO::getShiftType, ShiftTypeEnum.MIGRATE_FROM_HRMS.getCode());

            return userShiftConfigDao.update(null, updateWrapper);
        } catch (Exception e) {
            log.error("批量更新正常排班表失败，ID数量: {}", ids.size(), e);
            throw e;
        }
    }

    /**
     * 批量更新历史排班表指定ID的记录
     */
    public boolean updateHistoryTableBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }

        try {
            LambdaUpdateWrapper<UserShiftConfigHistoryDO> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.in(UserShiftConfigHistoryDO::getId, ids)
                    .set(UserShiftConfigHistoryDO::getShiftType, ShiftTypeEnum.MIGRATE_FROM_HRMS.getCode());

            return userShiftConfigHistoryDao.update(null, updateWrapper);
        } catch (Exception e) {
            log.error("批量更新历史排班表失败，ID数量: {}", ids.size(), e);
            throw e;
        }
    }

    /**
     * 根据用户ID列表和日期范围迁移数据,先删除这些用户的历史排班数据（从startDayId开始）
     * 查询指定用户在指定日期范围内的HR排班数据，并迁移转换为新系统的UserShiftConfigDO实体对象
     *
     * @param userIds    用户ID列表
     * @param startDayId 开始日期ID（日期标识）
     * @param endDayId   结束日期ID（日期标识）
     * @return 迁移结果，true表示成功，false表示失败
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    @Override
    @DSTransactional
    public Boolean migrateByUserIdsAndDateRange(List<Long> userIds, Long startDayId, Long endDayId) {
        log.info("开始执行指定用户数据迁移, 用户数量: {}, dayId范围: {} - {}",
                userIds != null ? userIds.size() : 0, startDayId, endDayId);
        XxlJobLogger.log("开始执行指定用户数据迁移, 用户数量: {}, dayId范围: {} - {}",
                userIds != null ? userIds.size() : 0, startDayId, endDayId);

        try {
            // 验证参数
            if (CollectionUtils.isEmpty(userIds)) {
                log.error("用户ID列表不能为空");
                XxlJobLogger.log("用户ID列表不能为空");
                return false;
            }

            if (startDayId != null && endDayId != null && startDayId > endDayId) {
                log.error("开始日期不能大于结束日期, startDayId: {}, endDayId: {}", startDayId, endDayId);
                XxlJobLogger.log("开始日期不能大于结束日期, startDayId: {}, endDayId: {}", startDayId, endDayId);
                return false;
            }

            // 删除这些用户的历史排班数据
            deleteShiftDataByUserIds(userIds, startDayId);
            log.info("已删除 {} 个用户的历史排班数据, startDayId: {}", userIds.size(), startDayId);
            XxlJobLogger.log("已删除 {} 个用户的历史排班数据, startDayId: {}", userIds.size(), startDayId);

            // 执行指定用户数据迁移
            Boolean result = migrationProcessor.migrateByUserIdsAndDateRange(userIds, startDayId, endDayId);

            if (Boolean.TRUE.equals(result)) {
                log.info("指定用户数据迁移完成, 用户数量: {}", userIds.size());
                XxlJobLogger.log("指定用户数据迁移完成, 用户数量: {}", userIds.size());
            } else {
                log.error("指定用户数据迁移失败, 用户数量: {}", userIds.size());
                XxlJobLogger.log("指定用户数据迁移失败, 用户数量: {}", userIds.size());
            }

            return result;

        } catch (Exception e) {
            log.error("指定用户数据迁移失败, 用户数量: {}", userIds != null ? userIds.size() : 0, e);
            XxlJobLogger.log("指定用户数据迁移失败, 用户数量: {}, error: {}",
                    userIds != null ? userIds.size() : 0, e.getMessage());
            return false;
        }
    }

    /**
     * 根据国家和入职确认时间范围执行排班数据迁移
     *
     * @param locationCountry 国家
     * @param confirmDayId    入职确认开始时间，用于筛选在此时间之后入职确认的用户
     * @param shiftEndDayId   排班迁移的结束时间（Long类型的dayId格式，如20250101）
     * @return 迁移结果，true表示成功，false表示失败
     */
    @Override
    public Boolean migrateByCountryAndConfirmDate(String locationCountry, Long confirmDayId, Long shiftEndDayId) {
        log.info("开始执行基于国家和入职确认时间的排班数据迁移, locationCountry: {}, confirmDate: {}, shiftEndDate: {}",
                locationCountry, confirmDayId, shiftEndDayId);
        XxlJobLogger.log("开始执行基于国家和入职确认时间的排班数据迁移, locationCountry: {}, confirmDate: {}, shiftEndDate: {}",
                locationCountry, confirmDayId, shiftEndDayId);

        try {
            // 参数验证
            if (StringUtils.isBlank(locationCountry)) {
                log.error("国家编码不能为空");
                XxlJobLogger.log("国家编码不能为空");
                return false;
            }

            if (confirmDayId == null) {
                log.error("入职确认时间不能为空");
                XxlJobLogger.log("入职确认时间不能为空");
                return false;
            }

            if (shiftEndDayId == null) {
                log.error("排班迁移结束时间不能为空");
                XxlJobLogger.log("排班迁移结束时间不能为空");
                return false;
            }

            // 第一步：调用 MigrationService 查找符合条件的用户
            Date confirmDate = DateHelper.transferDayIdToDate(confirmDayId);
            log.info("开始查找符合条件的用户, locationCountry: {}, confirmDate: {}", locationCountry, confirmDate);
            XxlJobLogger.log("开始查找符合条件的用户, locationCountry: {}, confirmDate: {}", locationCountry, confirmDate);

            List<UserEntryInfoDTO> userEntryList = migrationService.findUsersByCountryAndConfirmDate(locationCountry, confirmDate);

            if (CollectionUtils.isEmpty(userEntryList)) {
                log.info("未找到符合条件的用户，迁移完成, locationCountry: {}, confirmDayId: {}", locationCountry, confirmDate);
                XxlJobLogger.log("未找到符合条件的用户，迁移完成, locationCountry: {}, confirmDayId: {}", locationCountry, confirmDate);
                return true;
            }

            // 第二步：提取用户ID列表
            List<Long> userIds = userEntryList.stream()
                    .map(UserEntryInfoDTO::getUserId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(userIds)) {
                log.error("提取用户ID列表为空，无法执行迁移");
                XxlJobLogger.log("提取用户ID列表为空，无法执行迁移");
                return false;
            }

            log.info("成功提取用户ID列表，用户数量: {}", userIds.size());
            XxlJobLogger.log("成功提取用户ID列表，用户数量: {}", userIds.size());

            // 第三步：调用现有的 migrateByUserIdsAndDateRange 方法执行排班迁移
            log.info("开始执行排班数据迁移, 用户数量: {}, 时间范围: {} - {}", userIds.size(), confirmDayId, shiftEndDayId);
            XxlJobLogger.log("开始执行排班数据迁移, 用户数量: {}, 时间范围: {} - {}", userIds.size(), confirmDayId, shiftEndDayId);

            Boolean migrationResult = migrateByUserIdsAndDateRange(userIds, confirmDayId, shiftEndDayId);

            if (Boolean.TRUE.equals(migrationResult)) {
                log.info("基于国家和入职确认时间的排班数据迁移完成, locationCountry: {}, 用户数量: {}",
                        locationCountry, userIds.size());
                XxlJobLogger.log("基于国家和入职确认时间的排班数据迁移完成, locationCountry: {}, 用户数量: {}",
                        locationCountry, userIds.size());
            } else {
                log.error("基于国家和入职确认时间的排班数据迁移失败, locationCountry: {}, 用户数量: {}",
                        locationCountry, userIds.size());
                XxlJobLogger.log("基于国家和入职确认时间的排班数据迁移失败, locationCountry: {}, 用户数量: {}",
                        locationCountry, userIds.size());
            }

            return migrationResult;

        } catch (Exception e) {
            log.error("基于国家和入职确认时间的排班数据迁移异常, locationCountry: {}, confirmDayId: {}, shiftEndDate: {}",
                    locationCountry, confirmDayId, shiftEndDayId, e);
            XxlJobLogger.log("基于国家和入职确认时间的排班数据迁移异常, locationCountry: {}, confirmDayId: {}, shiftEndDate: {}, error: {}",
                    locationCountry, confirmDayId, shiftEndDayId, e.getMessage());
            return false;
        }
    }

}
