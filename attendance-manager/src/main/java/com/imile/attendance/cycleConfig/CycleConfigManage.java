package com.imile.attendance.cycleConfig;

import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigQuery;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/23 
 * @Description
 */
public interface CycleConfigManage {

    /**
     * 根据条件查询考勤周期配置
     */
    List<AttendanceCycleConfigDO> selectByCondition(AttendanceCycleConfigQuery query);

    /**
     * 根据国家和考勤周期类型查询考勤周期配置
     */
    List<AttendanceCycleConfigDO> selectByCountiesAndCycleType(List<String> countries, Integer cycleType);


    /**
     * 查询用户的考勤周期的补卡次数
     */
    List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList);


    List<AttendanceCycleConfigDO> getAllEnabled();
}
