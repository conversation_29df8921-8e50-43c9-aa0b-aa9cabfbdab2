package com.imile.attendance.migration.service.impl;

import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingPunchClassConfigMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.migration.dto.HrAttendanceClassConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.service.MappingPunchClassConfigService;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/16
 * @Description 考勤班次配置映射服务实现类
 */
@Slf4j
@Service
public class MappingPunchClassConfigServiceImpl implements MappingPunchClassConfigService {

    @Resource
    private MappingPunchClassConfigDao mappingPunchClassConfigDao;
    @Resource
    private MappingPunchClassConfigMapper mappingPunchClassConfigMapper;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    @Override
    public boolean save(MappingPunchClassConfigDO mappingPunchClassConfig) {
        return mappingPunchClassConfigDao.save(mappingPunchClassConfig);
    }

    @Override
    public boolean batchSave(List<MappingPunchClassConfigDO> mappingPunchClassConfigs) {
        mappingPunchClassConfigMapper.insertBatchSomeColumn(mappingPunchClassConfigs);
        return true;
    }

    @Override
    public MappingPunchClassConfigDO getByHrPunchClassId(Long hrPunchClassId) {
        return mappingPunchClassConfigDao.getByHrPunchClassId(hrPunchClassId);
    }

    @Override
    public List<MappingPunchClassConfigDO> listByHrPunchConfigId(Long hrPunchConfigId) {
        return mappingPunchClassConfigDao.listByHrPunchConfigId(hrPunchConfigId);
    }

    @Override
    public MappingPunchClassConfigDO getByPunchClassConfigId(Long punchClassConfigId) {
        return mappingPunchClassConfigDao.getByPunchClassConfigId(punchClassConfigId);
    }

    @Override
    public List<MappingPunchClassConfigDO> listByCountry(String country) {
        return mappingPunchClassConfigDao.listByCountry(country);
    }

    @Override
    public MappingPunchClassConfigDO buildMapping(HrAttendanceGroupDTO groupDTO,
                                                  HrAttendanceClassConfigDTO classConfigDTO,
                                                  PunchClassConfigDO punchClassConfigDO) {
        log.debug("构建班次配置映射, hrGroupId: {}, hrClassId: {}, punchClassConfigId: {}",
                groupDTO.getId(), classConfigDTO.getId(), punchClassConfigDO.getId());

        MappingPunchClassConfigDO mappingDO = new MappingPunchClassConfigDO();
        mappingDO.setId(defaultIdWorker.nextId());
        
        // 设置国家信息
        mappingDO.setCountry(groupDTO.getCountry());
        
        // 设置HR相关信息
        mappingDO.setHrPunchConfigId(groupDTO.getId());
        mappingDO.setHrPunchClassId(classConfigDTO.getId());
        mappingDO.setHrClassName(classConfigDTO.getClassName());
        mappingDO.setHrClassType(classConfigDTO.getClassType());
        
        // 设置新规则相关信息
        mappingDO.setPunchClassConfigId(punchClassConfigDO.getId());
        mappingDO.setPunchClassConfigName(punchClassConfigDO.getClassName());

        // 设置基础字段
        BaseDOUtil.fillDOInsertByUsrOrSystem(mappingDO);
        
        return mappingDO;
    }

    @Override
    public boolean existsMapping(Long hrPunchClassId) {
        if (hrPunchClassId == null) {
            return false;
        }
        MappingPunchClassConfigDO mapping = getByHrPunchClassId(hrPunchClassId);
        return mapping != null;
    }

    @Override
    public List<MappingPunchClassConfigDO> listByHrPunchClassIds(List<Long> hrPunchClassIds) {
        return mappingPunchClassConfigDao.listByHrPunchClassIds(hrPunchClassIds);
    }

    @Override
    public List<Long> getPunchClassConfigIdsByHrPunchConfigIds(List<Long> hrPunchConfigIds) {
        if (CollectionUtils.isEmpty(hrPunchConfigIds)) {
            log.debug("获取班次配置ID失败，HR考勤组ID列表为空");
            return Collections.emptyList();
        }

        try {
            // 查询映射表获取班次配置映射
            List<MappingPunchClassConfigDO> mappings = mappingPunchClassConfigDao.listByHrPunchConfigIds(hrPunchConfigIds);

            if (CollectionUtils.isEmpty(mappings)) {
                log.debug("获取班次配置ID完成，未找到对应的映射关系, hrPunchConfigIds: {}", hrPunchConfigIds);
                return Collections.emptyList();
            }

            // 提取班次配置ID列表
            List<Long> punchClassConfigIds = mappings.stream()
                    .map(MappingPunchClassConfigDO::getPunchClassConfigId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            log.debug("获取班次配置ID完成, 输入HR考勤组数量: {}, 输出班次配置数量: {}",
                    hrPunchConfigIds.size(), punchClassConfigIds.size());

            return punchClassConfigIds;

        } catch (Exception e) {
            log.error("获取班次配置ID失败, hrPunchConfigIds: {}", hrPunchConfigIds, e);
            return Collections.emptyList();
        }
    }
}
