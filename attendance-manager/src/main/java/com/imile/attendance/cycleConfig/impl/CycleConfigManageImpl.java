package com.imile.attendance.cycleConfig.impl;

import com.imile.attendance.cycleConfig.CycleConfigManage;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleConfigDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigQuery;
import com.imile.attendance.infrastructure.repository.form.dao.UserCycleReissueCardCountDao;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/23 
 * @Description
 */
@Component
public class CycleConfigManageImpl implements CycleConfigManage {

    @Resource
    private AttendanceCycleConfigDao attendanceCycleConfigDao;
    @Resource
    private UserCycleReissueCardCountDao userCycleReissueCardCountDao;

    @Override
    public List<AttendanceCycleConfigDO> selectByCondition(AttendanceCycleConfigQuery query) {
        return attendanceCycleConfigDao.selectByCondition(query);
    }

    @Override
    public List<AttendanceCycleConfigDO> selectByCountiesAndCycleType(List<String> countries, Integer cycleType) {
        return attendanceCycleConfigDao.selectByCountiesAndCycleType(countries, cycleType);
    }

    @Override
    public List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList) {
        return userCycleReissueCardCountDao.selectByUserIdList(userIdList);
    }

    @Override
    public List<AttendanceCycleConfigDO> getAllEnabled() {
        return attendanceCycleConfigDao.getAllEnabled();
    }
}
