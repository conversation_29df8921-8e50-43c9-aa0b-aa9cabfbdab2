package com.imile.attendance.infrastructure.repository.cycleConfig.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 考勤周期实际时间表实体类
 *
 * <AUTHOR> chen
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_cycle_calendar")
@ApiModel(value = "AttendanceCycleCalendarDO", description = "考勤周期实际时间表")
public class AttendanceCycleCalendarDO extends BaseDO {

    private static final long serialVersionUID = -4141140801188598971L;
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private Integer attendanceYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer attendanceMonth;

    /**
     * 周期开始时间
     */
    @ApiModelProperty(value = "周期开始时间")
    private Date cycleStartDate;

    /**
     * 周期结束时间
     */
    @ApiModelProperty(value = "周期结束时间")
    private Date cycleEndDate;
}