package com.imile.attendance.abnormal;

import cn.hutool.core.collection.CollUtil;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.AttendanceEmployeeDetailAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.AttendanceEmployeeDetailSnapshotAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Component
public class AttendanceEmployeeDetailManage {
    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;
    @Resource
    private AttendanceEmployeeDetailSnapshotDao attendanceEmployeeDetailSnapshotDao;
    @Resource
    private AttendanceEmployeeDetailAdapter attendanceEmployeeDetailAdapter;
    @Resource
    private AttendanceEmployeeDetailSnapshotAdapter attendanceEmployeeDetailSnapshotAdapter;
    @Resource
    private EmployeeAbnormalAttendanceManage employeeAbnormalAttendanceManage;


    public Map<Long, List<AttendanceEmployeeDetailDO>> mapByUserIdsAndDayId(List<Long> userIds, Long dayId) {
        List<AttendanceEmployeeDetailDO> attendanceEmployeeList = attendanceEmployeeDetailDao.selectByUserId(userIds, dayId);
        return attendanceEmployeeList.stream().collect(Collectors.groupingBy(AttendanceEmployeeDetailDO::getUserId));
    }


    /**
     * 根据申请单主键查询 (原有manage方法)
     *
     * @param formIdList
     * @return
     */
    public List<AttendanceEmployeeDetailDO> selectByFormIdList(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return new ArrayList<>();
        }
        return attendanceEmployeeDetailDao.selectByFormIdList(formIdList);
    }


    /**
     * 查询员工该年的所有出勤明细
     */
    public List<AttendanceEmployeeDetailDO> selectByYear(Long userId, Long year) {
        return attendanceEmployeeDetailDao.selectByYear(userId, year);
    }

    /**
     * 查询员工某天的考勤记录
     */
    public List<AttendanceEmployeeDetailDO> selectByDayId(Long userId, Long dayId){
        return attendanceEmployeeDetailDao.selectByDayId(userId, dayId);
    }



    /**
     * 查询用户指定时间段内的考勤记录
     */
    public List<AttendanceEmployeeDetailDO> selectAttendanceByDayId(Long userId, Long startDayId, Long endDayId) {
        return attendanceEmployeeDetailDao.selectAttendanceByDayId(userId, startDayId, endDayId);
    }


    /**
     * 查询用户指定时间的考勤记录
     */
    public List<AttendanceEmployeeDetailDO> selectAttendanceByDayIdList(Long userId, List<Long> dayIdList) {
        return attendanceEmployeeDetailDao.selectAttendanceByDayIdList(userId, dayIdList);
    }

    /**
     * 批量查询用户指定时间的考勤记录
     */
    public List<AttendanceEmployeeDetailDO> selectByUserIdListAndDayIdList(List<Long> userIdList, List<Long> dayIdList) {
        return attendanceEmployeeDetailDao.selectByUserIdListAndDayIdList(userIdList, dayIdList);
    }


    /**
     * 每日考勤计算落库
     */
    @Transactional(rollbackFor = Exception.class)
    public void attendanceGenerateUpdate(List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                         List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList,
                                         List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList,
                                         List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList) {

        if (CollectionUtils.isNotEmpty(addEmployeeDetailDOList)) {
            attendanceEmployeeDetailAdapter.saveBatch(addEmployeeDetailDOList);
        }
        if (CollectionUtils.isNotEmpty(updateEmployeeDetailDOList)) {
            attendanceEmployeeDetailAdapter.updateBatchById(updateEmployeeDetailDOList);
        }
        if (CollectionUtils.isNotEmpty(addAbnormalAttendanceDOList)) {
            employeeAbnormalAttendanceManage.abnormalBatchSave(addAbnormalAttendanceDOList);
        }
        if (CollectionUtils.isNotEmpty(updateAbnormalAttendanceDOList)) {
            employeeAbnormalAttendanceManage.abnormalBatchUpdate(updateAbnormalAttendanceDOList);
        }
    }

    /**
     * 考勤生成落库快照
     *
     * @param targetEmployeeDetailSnapshot     正常考勤快照
     * @param targetAbnormalAttendanceSnapshot 异常考勤快照
     */
    @Transactional(rollbackFor = Exception.class)
    public void attendanceGenerateSnapshotSaveOrUpdate(List<AttendanceEmployeeDetailSnapshotDO> targetEmployeeDetailSnapshot,
                                                       List<EmployeeAbnormalAttendanceSnapshotDO> targetAbnormalAttendanceSnapshot) {
        if (CollUtil.isNotEmpty(targetEmployeeDetailSnapshot)) {
            attendanceEmployeeDetailSnapshotAdapter.saveBatch(targetEmployeeDetailSnapshot);
        }
        if (CollUtil.isNotEmpty(targetAbnormalAttendanceSnapshot)) {
            employeeAbnormalAttendanceManage.abnormalSnapshotBatchSave(targetAbnormalAttendanceSnapshot);
        }
    }

    /**
     * 正常快照
     * @param userIdList
     * @param dayIdList
     * @return
     */
    public Map<Long, List<AttendanceEmployeeDetailSnapshotDO>> mapSnapShotByUserIdsAndDayIds(List<Long> userIdList, List<Long> dayIdList) {
        List<AttendanceEmployeeDetailSnapshotDO> abnormalSnapShotAttendanceDOList = attendanceEmployeeDetailSnapshotDao.selectByUserIdListAndDayIdList(userIdList, dayIdList)
                .stream()
                .collect(Collectors.toList());
        return abnormalSnapShotAttendanceDOList.stream().collect(Collectors.groupingBy(AttendanceEmployeeDetailSnapshotDO::getUserId));
    }
}
