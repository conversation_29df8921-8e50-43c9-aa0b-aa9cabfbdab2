package com.imile.attendance.migration;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.hrms.RpcUserClient;
import com.imile.attendance.hrms.dto.UserDynamicInfoRpcDTO;
import com.imile.attendance.hrms.support.RpcUserClientSupport;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> chen
 * @Date 2025/08/06
 * @Time 11:28
 * @Description
 */
public class RpcUserClientSupportTest extends BaseTest {

    @Resource
    private RpcUserClientSupport rpcUserClientSupport;

    @Test
    public void testListUserDynamicInfo() {
        String userCode = "2103429201";
        List<String> userCodes = Stream.iterate(0, i -> i + 1)
                .map(k -> k + "").limit(1000).collect(Collectors.toList());
        userCodes.add(userCode);
        List<UserDynamicInfoRpcDTO> userDynamicInfoRpcDTOList = rpcUserClientSupport.listUserDynamicInfo(userCodes);
        System.out.println(userDynamicInfoRpcDTOList);
    }

    @Test
    public void testlistUserDynamicInfoById(){
        Long userId = 1103011271906697217L;
        List<Long> userIds = Stream.iterate(0, i -> i + 1)
                .map(Long::valueOf).limit(1000)
                .collect(Collectors.toList());
        userIds.add(userId);
        List<UserDynamicInfoRpcDTO> userDynamicInfoRpcDTOList = rpcUserClientSupport.listUserDynamicInfoById(userIds);
        System.out.println(userDynamicInfoRpcDTOList);
    }
}
