package com.imile.attendance.punch;

import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.util.DateHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Component
public class EmployeePunchRecordManage {

    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;

    public Map<String, List<UserPunchRecordBO>> mapByUserCodesAndTimeRange(Date startTime, Date endTime, List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList) || (Objects.isNull(startTime) && Objects.isNull(endTime))) {
            return Collections.emptyMap();
        }
        EmployeePunchCardRecordQuery query = new EmployeePunchCardRecordQuery();
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        query.setUserCodes(userCodeList);
        List<EmployeePunchRecordDO> allPunchRecordList = employeePunchRecordDao.listRecords(query);
        List<UserPunchRecordBO> userPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            UserPunchRecordBO userPunchRecordDTO = new UserPunchRecordBO();
            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
            userPunchRecordDTO.setFormatPunchTime( DateHelper.parseIgnoreSeconds(employeePunchRecordDO.getPunchTime()));
            userPunchRecordDTOList.add(userPunchRecordDTO);
        }
        return userPunchRecordDTOList.stream().distinct().collect(Collectors.groupingBy(UserPunchRecordBO::getUserCode));
    }

    /**
     * 批量获取用户在指定时间范围内的打卡记录
     */
    public List<EmployeePunchRecordDO> getUsersPunchRecordsInTimeRange(List<String> userCodes, List<String> dayIds) {
        if (CollectionUtils.isEmpty(userCodes) || CollectionUtils.isEmpty(dayIds)) {
            return Collections.emptyList();
        }
        EmployeePunchCardRecordQuery punchCardRecordQuery = EmployeePunchCardRecordQuery.builder()
                .dayIds(dayIds)
                .userCodes(userCodes)
                .build();
        return employeePunchRecordDao.listRecords(punchCardRecordQuery);
    }

    /**
     * 获取最近一条绑定手机的打卡记录
     */
    public List<EmployeePunchRecordDO> getPunchListByMobileConfigId(List<Long> mobileConfigId) {
        return employeePunchRecordDao.getPunchListByMobileConfigId(mobileConfigId);
    }

    /**
     * 根据数据来源sourceType和day_id查询打卡记录
     */
    public List<EmployeePunchRecordDO> listBySourceTypeAndDayId(String sourceType, List<String> dayIds) {
        return employeePunchRecordDao.listBySourceTypeAndDayId(sourceType, dayIds);
    }


    /**
     * 获取用户在指定时间范围内的打卡记录
     */
    public List<EmployeePunchRecordDO> getUserPunchRecordInTimeRange(String userCode, Date startTime, Date endTime){
        EmployeePunchCardRecordQuery punchCardRecordQuery = EmployeePunchCardRecordQuery.builder()
                .userCode(userCode)
                .startTime(startTime)
                .endTime(endTime)
                .build();
        return employeePunchRecordDao.listRecords(punchCardRecordQuery);
    }


    /**
     * 获取用户对应考勤日下的所有打卡记录
     */
    public List<EmployeePunchRecordDO> getUserPunchRecords(String userCode, List<Long> dayIds){
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(dayIds)) {
            return Collections.emptyList();
        }
        //获取打卡规则的起始结束时间的打卡数据
        EmployeePunchCardRecordQuery punchCardRecordQuery = new EmployeePunchCardRecordQuery();
        punchCardRecordQuery.setDayIds(
                dayIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList())
        );
        punchCardRecordQuery.setUserCode(userCode);
        List<EmployeePunchRecordDO> allPunchRecordList = employeePunchRecordDao.listRecords(punchCardRecordQuery)
                .stream()
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allPunchRecordList)) {
            return Collections.emptyList();
        }
        return allPunchRecordList;
    }
}
