package com.imile.attendance.hrms.dto;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/08/05
 * @Time 19:25
 * @Description
 */
@Data
public class UserDynamicInfoRpcDTO {

    /**
     * 人员ID（为了满足历史功能，不建议下游使用或存储，统一使用userCode）
     */
    private Long id;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 人员名称
     */
    private String userName;

    /**
     * 是否司机（0:否 1:是）
     */
    private Integer isDriver;

    /**
     * 用工类型
     *
     * @see com.imile.hrms.api.primary.enums.EmploymentTypeEnum
     */
    private String employeeType;

    /**
     * 账号状态
     *
     * @see com.imile.hrms.api.primary.enums.CommonStatusEnum
     */
    private String status;

    /**
     * 工作状态（在职：ON_JOB 离职：DIMISSION）
     */
    private String workStatus;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;

    /**
     * 部门编码
     */
    private String deptCode;
}
