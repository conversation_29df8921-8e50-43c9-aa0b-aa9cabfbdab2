package com.imile.attendance.cycleConfig.dto;

import lombok.Data;

import java.util.Date;

/**
 * 考勤周期日历数据传输对象
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Data
public class AttendanceCycleCalendarDataDTO {

    /**
     * 国家代码
     */
    private String country;

    /**
     * 考勤年份
     */
    private Integer attendanceYear;

    /**
     * 考勤月份
     */
    private Integer attendanceMonth;

    /**
     * 周期开始日期
     */
    private Date cycleStartDate;

    /**
     * 周期结束日期
     */
    private Date cycleEndDate;

    /**
     * 是否跨年周期
     */
    private Boolean crossYear;

    /**
     * 数据生成时间
     */
    private Date generateTime;

    /**
     * 构造方法
     */
    public AttendanceCycleCalendarDataDTO() {
        this.generateTime = new Date();
    }

    /**
     * 静态构造方法
     *
     * @param country 国家代码
     * @param year 年份
     * @param month 月份
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return DTO对象
     */
    public static AttendanceCycleCalendarDataDTO of(String country, Integer year, Integer month, 
                                                   Date startDate, Date endDate) {
        AttendanceCycleCalendarDataDTO dto = new AttendanceCycleCalendarDataDTO();
        dto.setCountry(country);
        dto.setAttendanceYear(year);
        dto.setAttendanceMonth(month);
        dto.setCycleStartDate(startDate);
        dto.setCycleEndDate(endDate);
        return dto;
    }
}
