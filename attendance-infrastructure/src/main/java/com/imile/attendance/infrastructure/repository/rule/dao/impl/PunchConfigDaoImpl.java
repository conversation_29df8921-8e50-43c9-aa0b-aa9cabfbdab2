package com.imile.attendance.infrastructure.repository.rule.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.PunchConfigMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigQuery;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Component
@RequiredArgsConstructor
public class PunchConfigDaoImpl extends ServiceImpl<PunchConfigMapper, PunchConfigDO> implements PunchConfigDao {

    @Override
    public PunchConfigDO getByName(String name) {
        LambdaQueryWrapper<PunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigDO::getConfigName, name)
                .eq(PunchConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(PunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(PunchConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<PunchConfigDO> getByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigDO::getCountry, country)
                .eq(PunchConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(PunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(PunchConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchConfigDO> getByCountries(List<String> countryList) {
        if (CollectionUtils.isEmpty(countryList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigDO::getCountry, countryList)
                .eq(PunchConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(PunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(PunchConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchConfigDO> listByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .in(PunchConfigDO::getId, configIdList);
        return list(queryWrapper);
    }

    @Override
    public List<PunchConfigDO> listLatestByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigDO::getId, configIdList);
        queryWrapper.eq(PunchConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public PunchConfigDO getLatestByConfigNo(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return null;
        }
        LambdaQueryWrapper<PunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigDO::getConfigNo, configNo);
        queryWrapper.eq(PunchConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<PunchConfigDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public List<PunchConfigDO> listLatestByConfigNos(List<String> configNos) {
        if (CollectionUtils.isEmpty(configNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigDO::getConfigNo, configNos);
        queryWrapper.eq(PunchConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchConfigDO> listByConfigNo(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigDO::getConfigNo, configNo);
        queryWrapper.eq(PunchConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchConfigDO> listByQuery(PunchConfigQuery query) {
        LambdaQueryWrapper<PunchConfigDO> queryWrapper = Wrappers.lambdaQuery();

        if (CollectionUtils.isNotEmpty(query.getPunchConfigIds())) {
            queryWrapper.in(PunchConfigDO::getId, query.getPunchConfigIds());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(PunchConfigDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(PunchConfigDO::getCountry, query.getCountryList());
        }
        if (StringUtils.isNotBlank(query.getPunchConfigType())) {
            queryWrapper.eq(PunchConfigDO::getConfigType, query.getPunchConfigType());
        }
        if (Objects.nonNull(query.getPunchConfigNos())) {
            queryWrapper.in(PunchConfigDO::getConfigNo, query.getPunchConfigNos());
        }
        if (StringUtils.isNotBlank(query.getPunchConfigName())) {
            queryWrapper.like(PunchConfigDO::getConfigName, query.getPunchConfigName());
        }
        if (Objects.nonNull(query.getDeptId())) {
            queryWrapper.and(i ->
                    i.apply("FIND_IN_SET('" + query.getDeptId().toString() + "', dept_ids)")
            );
        }
        if (CollectionUtils.isNotEmpty(query.getDeptIds())) {
            queryWrapper.and(i -> query.getDeptIds()
                    .forEach(deptId -> i.apply("FIND_IN_SET('" + deptId.toString() + "', dept_ids)").or()));
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(PunchConfigDO::getStatus, query.getStatus());
        }
        queryWrapper.eq(PunchConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(PunchConfigDO::getIsLatest, BusinessConstant.Y);
        // 修改：按照创建时间倒序排序
        queryWrapper.orderByDesc(PunchConfigDO::getCreateDate);

        return list(queryWrapper);
    }

    @Override
    public List<PunchConfigDO> listCountryLevelConfigsByCountries(List<String> countryList) {
        return this.baseMapper.listCountryLevelConfigsByCountries(countryList);
    }

    @Override
    public List<PunchConfigDO> pageQuery(PunchConfigPageQuery query) {
        return this.baseMapper.pageQuery(query);
    }
}
