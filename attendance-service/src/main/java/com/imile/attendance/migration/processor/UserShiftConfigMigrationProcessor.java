package com.imile.attendance.migration.processor;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendanceClassEmployeeConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceClassEmployeeConfigDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigHistoryDao;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigHistoryDO;
import com.imile.attendance.migration.adapter.UserShiftConfigAdapter;
import com.imile.attendance.migration.converter.UserShiftConfigDataConverter;
import com.imile.attendance.migration.service.MappingPunchClassConfigService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 排班数据迁移处理器
 * 负责具体的数据迁移逻辑
 * 
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Slf4j
@Component
public class UserShiftConfigMigrationProcessor {
    
    @Resource
    private HrmsAttendanceClassEmployeeConfigDao hrmsDao;

    @Resource
    private HrmsAttendancePunchConfigDao hrmsAttendancePunchConfigDao;

    @Resource
    private UserShiftConfigDao userShiftConfigDao;

    @Resource
    private UserShiftConfigHistoryDao userShiftConfigHistoryDao;

    @Resource
    private UserShiftConfigAdapter userShiftConfigAdapter;

    @Resource
    private UserShiftConfigDataConverter dataConverter;

    @Resource
    private MappingPunchClassConfigService mappingPunchClassConfigService;

    /**
     * 处理历史数据批次
     * 优化版本：使用批量转换避免N+1查询问题
     */
    private MigrationResult processHistoryDataBatch(List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataList) {
        int successCount = 0;
        int failedCount = 0;

        try {
            long convertStartTime = System.currentTimeMillis();

            // 使用批量转换方法，避免N+1查询问题
            List<UserShiftConfigHistoryDO> historyDataList = dataConverter.convertFromHrmsToHistoryBatch(hrmsDataList);

            // 过滤验证数据
            List<UserShiftConfigHistoryDO> validDataList = historyDataList.stream()
                    .filter(Objects::nonNull)
                    .filter(dataConverter::validateConvertedHistoryData)
                    .collect(Collectors.toList());

            long convertEndTime = System.currentTimeMillis();
            log.debug("历史数据批次转换完成, 输入: {}, 转换后: {}, 验证通过: {}, 转换耗时: {}ms",
                    hrmsDataList.size(), historyDataList.size(), validDataList.size(),
                    convertEndTime - convertStartTime);

            // 批量插入历史表
            if (CollectionUtils.isNotEmpty(validDataList)) {
                batchInsertHistory(validDataList);
                successCount = validDataList.size();
                failedCount = hrmsDataList.size() - successCount;
            } else {
                failedCount = hrmsDataList.size();
            }

        } catch (Exception e) {
            log.error("处理历史数据批次失败, 批次大小: {}", hrmsDataList.size(), e);
            failedCount = hrmsDataList.size();
        }

        return new MigrationResult(successCount, failedCount);
    }
    
    /**
     * 处理当前数据批次
     */
    private MigrationResult processCurrentDataBatch(List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataList) {
        int successCount = 0;
        int failedCount = 0;

        try {
            long convertStartTime = System.currentTimeMillis();

            // 使用批量转换方法
            List<UserShiftConfigDO> newDataList = dataConverter.convertFromHrmsBatch(hrmsDataList);

            // 过滤验证数据
            List<UserShiftConfigDO> validDataList = newDataList.stream()
                    .filter(Objects::nonNull)
                    .filter(dataConverter::validateConvertedData)
                    .collect(Collectors.toList());

            long convertEndTime = System.currentTimeMillis();
            log.info("当前数据批次转换完成, 输入: {}, 转换后: {}, 验证通过: {}, 转换耗时: {}ms",
                    hrmsDataList.size(), newDataList.size(), validDataList.size(),
                    convertEndTime - convertStartTime);

            // 批量插入新表
            if (CollectionUtils.isNotEmpty(validDataList)) {
                batchInsertNew(validDataList);
                successCount = validDataList.size();
                failedCount = hrmsDataList.size() - successCount;
            } else {
                failedCount = hrmsDataList.size();
            }

        } catch (Exception e) {
            log.error("处理当前数据批次失败, 批次大小: {}", hrmsDataList.size(), e);
            failedCount = hrmsDataList.size();
        }

        return new MigrationResult(successCount, failedCount);
    }
    
    /**
     * 批量插入历史数据
     */
    private void batchInsertHistory(List<UserShiftConfigHistoryDO> historyDataList) {
        // 按批量大小分批插入
        List<List<UserShiftConfigHistoryDO>> partitionList = Lists.partition(
                historyDataList, BusinessConstant.MAX_BATCH_SIZE);
        
        for (List<UserShiftConfigHistoryDO> batch : partitionList) {
            try {
                userShiftConfigHistoryDao.saveBatch(batch);
                log.debug("批量插入历史数据成功, 数量: {}", batch.size());
            } catch (Exception e) {
                log.error("批量插入历史数据失败, 数量: {}", batch.size(), e);
                throw e;
            }
        }
    }
    
    /**
     * 批量插入新数据
     * 使用适配器模式，支持在原始表和migrate表之间透明切换
     */
    private void batchInsertNew(List<UserShiftConfigDO> newDataList) {
        List<List<UserShiftConfigDO>> partitionList = Lists.partition(
                newDataList, BusinessConstant.MAX_BATCH_SIZE);

        for (List<UserShiftConfigDO> batch : partitionList) {
            try {
                // 使用适配器进行批量保存，支持配置驱动的表切换
                userShiftConfigAdapter.saveBatch(batch);
                log.info("批量插入新数据成功, 数量: {}, 当前使用{}表",
                        batch.size(), userShiftConfigAdapter.isShouldUseMigrate() ? "migrate" : "原始");
            } catch (Exception e) {
                log.error("批量插入新数据失败, 数量: {}, 当前使用{}表",
                        batch.size(), userShiftConfigAdapter.isShouldUseMigrate() ? "migrate" : "原始", e);
                throw e;
            }
        }
    }

    /**
     * 迁移历史数据到历史表（按国家分组处理）
     * 重构版本：参考migrateCurrentData方法，按国家分组处理历史数据，保持处理逻辑一致性
     *
     * @param country 国家代码
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 迁移结果
     */
    public Boolean migrateHistoryData(String country, Long startDayId, Long endDayId) {
        log.info("开始迁移历史数据到历史表, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);
        XxlJobLogger.log("开始迁移历史数据到历史表, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);

        try {
            // 第一步：获取该国家的考勤组ID列表
            List<Long> punchConfigIds = getPunchConfigIdsByCountry(country);
            if (CollectionUtils.isEmpty(punchConfigIds)) {
                log.info("历史数据迁移完成, country: {}, 该国家无考勤组配置", country);
                XxlJobLogger.log("历史数据迁移完成, country: {}, 该国家无考勤组配置", country);
                return true;
            }

            // 第二步：统计总数据量
            Long totalCount = hrmsDao.countByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId);
            log.info("历史数据迁移统计, country: {}, 总记录数: {}", country, totalCount);
            XxlJobLogger.log("历史数据迁移统计, country: {}, 总记录数: {}", country, totalCount);

            if (totalCount == 0) {
                log.info("历史数据迁移完成, country: {}, 无数据需要迁移", country);
                XxlJobLogger.log("历史数据迁移完成, country: {}, 无数据需要迁移", country);
                return true;
            }

            // 第三步：分页处理数据
            int pageSize = BusinessConstant.ShiftMigration.MIGRATION_PAGE_SIZE;
            int currentPage = 1;
            int totalProcessed = 0;
            int totalSuccess = 0;
            int totalFailed = 0;

            while (true) {
                long pageStartTime = System.currentTimeMillis();

                // 使用PageHelper进行分页查询，基于考勤组ID列表查询
                PageInfo<HrmsAttendanceClassEmployeeConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                        .doSelectPageInfo(() -> hrmsDao.pageByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId));

                List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataList = pageInfo.getList();

                if (CollectionUtils.isEmpty(hrmsDataList)) {
                    log.info("历史数据分页查询结果为空，结束迁移, currentPage: {}", currentPage);
                    break;
                }

                long pageEndTime = System.currentTimeMillis();

                // 数据转换和批量插入
                MigrationResult result = processHistoryDataBatch(hrmsDataList);
                totalProcessed += hrmsDataList.size();
                totalSuccess += result.getSuccessCount();
                totalFailed += result.getFailedCount();

                log.info("历史数据迁移进度, country: {}, 当前页: {}, 本页处理: {}, 本页成功: {}, 本页失败: {}, 累计处理: {}, 查询耗时: {}ms",
                        country, currentPage, hrmsDataList.size(), result.getSuccessCount(),
                        result.getFailedCount(), totalProcessed, pageEndTime - pageStartTime);
                XxlJobLogger.log("历史数据迁移进度, country: {}, 当前页: {}, 本页处理: {}, 本页成功: {}, 本页失败: {}, 累计处理: {}, 查询耗时: {}ms",
                        country, currentPage, hrmsDataList.size(), result.getSuccessCount(),
                        result.getFailedCount(), totalProcessed, pageEndTime - pageStartTime);

                // 检查是否为最后一页
                if (currentPage >= pageInfo.getPages()) {
                    break;
                }
                currentPage++;
            }

            log.info("历史数据迁移完成, country: {}, 总计处理: {}, 成功: {}, 失败: {}",
                    country, totalProcessed, totalSuccess, totalFailed);
            XxlJobLogger.log("历史数据迁移完成, country: {}, 总计处理: {}, 成功: {}, 失败: {}",
                    country, totalProcessed, totalSuccess, totalFailed);

            // 如果有失败的记录，返回false
            return totalFailed == 0;

        } catch (Exception e) {
            log.error("历史数据迁移失败, country: {}, dayId范围: {} - {}",
                    country, startDayId, endDayId, e);
            XxlJobLogger.log("历史数据迁移失败, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }

    /**
     * 根据国家获取考勤组ID列表
     *
     * @param country 国家代码
     * @return 考勤组ID列表
     */
    private List<Long> getPunchConfigIdsByCountry(String country) {
        long startTime = System.currentTimeMillis();

        try {
            // 查询该国家的所有考勤组配置
            List<HrmsAttendancePunchConfigDO> punchConfigs = hrmsAttendancePunchConfigDao.listByCountry(country);

            if (CollectionUtils.isEmpty(punchConfigs)) {
                log.warn("国家{}没有找到任何考勤组配置", country);
                XxlJobLogger.log("国家{}没有找到任何考勤组配置", country);
                return Collections.emptyList();
            }

            // 提取考勤组ID列表
            List<Long> punchConfigIds = punchConfigs.stream()
                    .map(HrmsAttendancePunchConfigDO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            long endTime = System.currentTimeMillis();
            log.info("查询国家{}的考勤组ID列表完成, 数量: {}, 耗时: {}ms",
                    country, punchConfigIds.size(), endTime - startTime);
            XxlJobLogger.log("查询国家{}的考勤组ID列表完成, 数量: {}, 耗时: {}ms",
                    country, punchConfigIds.size(), endTime - startTime);

            return punchConfigIds;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("查询国家{}的考勤组ID列表失败, 耗时: {}ms", country, endTime - startTime, e);
            XxlJobLogger.log("查询国家{}的考勤组ID列表失败, 耗时: {}ms, error: {}",
                    country, endTime - startTime, e.getMessage());
            throw e;
        }
    }

    /**
     * 迁移当前数据到新表
     *
     * @param country 国家代码
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 迁移结果
     */
    public Boolean migrateCurrentData(String country, Long startDayId, Long endDayId) {
        log.info("开始迁移当前数据到新表, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);
        XxlJobLogger.log("开始迁移当前数据到新表, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);

        try {
            // 第一步：获取该国家的考勤组ID列表
            List<Long> punchConfigIds = getPunchConfigIdsByCountry(country);
            if (CollectionUtils.isEmpty(punchConfigIds)) {
                log.info("当前数据迁移完成, country: {}, 该国家无考勤组配置", country);
                XxlJobLogger.log("当前数据迁移完成, country: {}, 该国家无考勤组配置", country);
                return true;
            }

            // 第二步：统计总数据量
            Long totalCount = hrmsDao.countByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId);
            log.info("当前数据迁移统计, country: {}, 总记录数: {}", country, totalCount);
            XxlJobLogger.log("当前数据迁移统计, country: {}, 总记录数: {}", country, totalCount);

            if (totalCount == 0) {
                log.info("当前数据迁移完成, country: {}, 无数据需要迁移", country);
                XxlJobLogger.log("当前数据迁移完成, country: {}, 无数据需要迁移", country);
                return true;
            }

            // 第三步：分页处理数据
            int pageSize = BusinessConstant.ShiftMigration.MIGRATION_PAGE_SIZE;
            int currentPage = 1;
            int totalProcessed = 0;
            int totalSuccess = 0;
            int totalFailed = 0;

            while (true) {
                long pageStartTime = System.currentTimeMillis();

                // 使用PageHelper进行分页查询
                PageInfo<HrmsAttendanceClassEmployeeConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                        .doSelectPageInfo(() -> hrmsDao.pageByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId));

                List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataList = pageInfo.getList();

                if (CollectionUtils.isEmpty(hrmsDataList)) {
                    log.info("当前数据分页查询结果为空，结束迁移, currentPage: {}", currentPage);
                    break;
                }

                long pageEndTime = System.currentTimeMillis();

                // 数据转换和批量插入
                MigrationResult result = processCurrentDataBatch(hrmsDataList);
                totalProcessed += hrmsDataList.size();
                totalSuccess += result.getSuccessCount();
                totalFailed += result.getFailedCount();

                log.info("当前数据迁移进度, country: {}, 当前页: {}, 本页处理: {}, 本页成功: {}, 本页失败: {}, 累计处理: {}, 查询耗时: {}ms",
                        country, currentPage, hrmsDataList.size(), result.getSuccessCount(),
                        result.getFailedCount(), totalProcessed, pageEndTime - pageStartTime);
                XxlJobLogger.log("当前数据迁移进度, country: {}, 当前页: {}, 本页处理: {}, 本页成功: {}, 本页失败: {}, 累计处理: {}, 查询耗时: {}ms",
                        country, currentPage, hrmsDataList.size(), result.getSuccessCount(),
                        result.getFailedCount(), totalProcessed, pageEndTime - pageStartTime);

                // 检查是否为最后一页
                if (currentPage >= pageInfo.getPages()) {
                    break;
                }
                currentPage++;
            }

            log.info("当前数据迁移完成, country: {}, 总计处理: {}, 成功: {}, 失败: {}",
                    country, totalProcessed, totalSuccess, totalFailed);
            XxlJobLogger.log("当前数据迁移完成, country: {}, 总计处理: {}, 成功: {}, 失败: {}",
                    country, totalProcessed, totalSuccess, totalFailed);

            // 如果有失败的记录，返回false
            return totalFailed == 0;

        } catch (Exception e) {
            log.error("当前数据迁移失败, country: {}, dayId范围: {} - {}",
                    country, startDayId, endDayId, e);
            XxlJobLogger.log("当前数据迁移失败, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }

    /**
     * 根据用户ID列表和日期范围迁移数据到新表
     * 支持批量处理、错误恢复和进度监控
     *
     * @param userIds 用户ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 迁移结果，true表示成功，false表示失败
     */
    public Boolean migrateByUserIdsAndDateRange(List<Long> userIds, Long startDayId, Long endDayId) {
        log.info("开始根据用户ID列表迁移数据到新表, 用户数量: {}, dayId范围: {} - {}",
                userIds.size(), startDayId, endDayId);
        XxlJobLogger.log("开始根据用户ID列表迁移数据到新表, 用户数量: {}, dayId范围: {} - {}",
                userIds.size(), startDayId, endDayId);

        long startTime = System.currentTimeMillis();

        try {
            // 第一步：统计总数据量
            Long totalCount = hrmsDao.countByUserIdListAndDateRange(userIds, startDayId, endDayId);
            log.info("用户ID列表数据统计完成, 用户数量: {}, 总记录数: {}, 耗时: {}ms",
                    userIds.size(), totalCount, System.currentTimeMillis() - startTime);
            XxlJobLogger.log("用户ID列表数据统计完成, 用户数量: {}, 总记录数: {}, 耗时: {}ms",
                    userIds.size(), totalCount, System.currentTimeMillis() - startTime);

            if (totalCount == 0) {
                log.info("没有找到需要迁移的数据, 用户数量: {}", userIds.size());
                XxlJobLogger.log("没有找到需要迁移的数据, 用户数量: {}", userIds.size());
                return true;
            }

            // 第二步：分页处理数据
            int pageSize = BusinessConstant.ShiftMigration.MIGRATION_PAGE_SIZE;
            int currentPage = 1;
            int totalProcessed = 0;
            int totalSuccess = 0;
            int totalFailed = 0;

            while (true) {
                long pageStartTime = System.currentTimeMillis();

                // 使用PageHelper进行分页查询，基于用户ID列表查询
                PageInfo<HrmsAttendanceClassEmployeeConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                        .doSelectPageInfo(() -> hrmsDao.pageByUserIdListAndDateRange(userIds, startDayId, endDayId));

                List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataList = pageInfo.getList();

                if (CollectionUtils.isEmpty(hrmsDataList)) {
                    log.info("用户ID列表数据分页查询结果为空，结束迁移, currentPage: {}", currentPage);
                    XxlJobLogger.log("用户ID列表数据分页查询结果为空，结束迁移, currentPage: {}", currentPage);
                    break;
                }

                // 处理当前批次数据
                MigrationResult batchResult = processCurrentDataBatch(hrmsDataList);
                totalProcessed += hrmsDataList.size();
                totalSuccess += batchResult.getSuccessCount();
                totalFailed += batchResult.getFailedCount();

                long pageEndTime = System.currentTimeMillis();
                log.info("用户ID列表数据第{}页处理完成, 本页数量: {}, 成功: {}, 失败: {}, 总进度: {}/{}, 耗时: {}ms",
                        currentPage, hrmsDataList.size(), batchResult.getSuccessCount(), batchResult.getFailedCount(),
                        totalProcessed, totalCount, pageEndTime - pageStartTime);
                XxlJobLogger.log("用户ID列表数据第{}页处理完成, 本页数量: {}, 成功: {}, 失败: {}, 总进度: {}/{}, 耗时: {}ms",
                        currentPage, hrmsDataList.size(), batchResult.getSuccessCount(), batchResult.getFailedCount(),
                        totalProcessed, totalCount, pageEndTime - pageStartTime);

                currentPage++;

                // 如果当前页数据量小于页大小，说明已经是最后一页
                if (hrmsDataList.size() < pageSize) {
                    break;
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("用户ID列表数据迁移完成, 用户数量: {}, 总处理: {}, 成功: {}, 失败: {}, 总耗时: {}ms",
                    userIds.size(), totalProcessed, totalSuccess, totalFailed, endTime - startTime);
            XxlJobLogger.log("用户ID列表数据迁移完成, 用户数量: {}, 总处理: {}, 成功: {}, 失败: {}, 总耗时: {}ms",
                    userIds.size(), totalProcessed, totalSuccess, totalFailed, endTime - startTime);

            // 如果有失败的记录，返回false
            return totalFailed == 0;

        } catch (Exception e) {
            log.error("用户ID列表数据迁移失败, 用户数量: {}, dayId范围: {} - {}",
                    userIds.size(), startDayId, endDayId, e);
            XxlJobLogger.log("用户ID列表数据迁移失败, 用户数量: {}, error: {}", userIds.size(), e.getMessage());
            throw e;
        }
    }

    /**
     * 回滚当前数据迁移操作
     *
     * 该方法用于回滚由 migrateCurrentData 方法创建的所有数据，执行物理删除操作。
     * 通过 taskFlag 字段精确识别需要回滚的迁移数据，使用批处理方式进行安全删除。
     *
     * @param country 国家代码，用于限定回滚范围
     * @param startDayId 开始日期ID，用于限定回滚的日期范围（可选）
     * @param endDayId 结束日期ID，用于限定回滚的日期范围（可选）
     * @return 回滚结果，true表示成功，false表示失败
     */
    public Boolean rollbackCurrentData(String country, Long startDayId, Long endDayId) {
        log.info("开始回滚当前数据迁移, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);
        XxlJobLogger.log("开始回滚当前数据迁移, country: {}, dayId范围: {} - {}",
                country, startDayId, endDayId);

        long startTime = System.currentTimeMillis();

        try {
            // 第一步：获取该国家的考勤组ID列表
            List<Long> punchConfigIds = getPunchConfigIdsByCountry(country);
            if (CollectionUtils.isEmpty(punchConfigIds)) {
                log.info("回滚操作完成, country: {}, 该国家无考勤组配置", country);
                XxlJobLogger.log("回滚操作完成, country: {}, 该国家无考勤组配置", country);
                return true;
            }

            // 第二步：通过映射表获取班次配置ID列表
            List<Long> punchClassConfigIds = getPunchClassConfigIdsByPunchConfigIds(punchConfigIds);
            if (CollectionUtils.isEmpty(punchClassConfigIds)) {
                log.info("回滚操作完成, country: {}, 该国家的考勤组无对应的班次配置", country);
                XxlJobLogger.log("回滚操作完成, country: {}, 该国家的考勤组无对应的班次配置", country);
                return true;
            }

            // 第三步：统计需要回滚的数据总数
            String taskFlagPrefix = BusinessConstant.ShiftMigration.MIGRATE_FROM_HRMS;
            Long totalCount = userShiftConfigAdapter.countMigratedDataForRollback(
                    punchClassConfigIds, startDayId, endDayId, taskFlagPrefix);

            log.info("回滚数据统计完成, country: {}, 考勤组数量: {}, 班次配置数量: {}, 总记录数: {}",
                    country, punchConfigIds.size(), punchClassConfigIds.size(), totalCount);
            XxlJobLogger.log("回滚数据统计完成, country: {}, 考勤组数量: {}, 班次配置数量: {}, 总记录数: {}",
                    country, punchConfigIds.size(), punchClassConfigIds.size(), totalCount);

            if (totalCount == 0) {
                log.info("回滚操作完成, country: {}, 无数据需要回滚", country);
                XxlJobLogger.log("回滚操作完成, country: {}, 无数据需要回滚", country);
                return true;
            }

            // 第四步：数据验证和确认展示
            log.info("=== 回滚数据确认信息 ===");
            log.info("国家: {}", country);
            log.info("考勤组数量: {}", punchConfigIds.size());
            log.info("班次配置数量: {}", punchClassConfigIds.size());
            log.info("日期范围: {} - {}", startDayId, endDayId);
            log.info("任务标识: {}", taskFlagPrefix);
            log.info("将要删除的记录数: {}", totalCount);
            log.info("当前使用的表: {}", userShiftConfigAdapter.isShouldUseMigrate() ? "migrate表" : "原始表");
            log.info("=== 确认信息结束 ===");

            XxlJobLogger.log("=== 回滚数据确认信息 ===");
            XxlJobLogger.log("国家: {}, 考勤组数量: {}, 班次配置数量: {}, 日期范围: {} - {}, 将要删除记录数: {}, 使用表: {}",
                    country, punchConfigIds.size(), punchClassConfigIds.size(), startDayId, endDayId, totalCount,
                    userShiftConfigAdapter.isShouldUseMigrate() ? "migrate表" : "原始表");
            XxlJobLogger.log("=== 确认信息结束 ===");

            // 第五步：分页批量删除处理
            int batchSize = BusinessConstant.MAX_BATCH_SIZE; // 1000条记录
            int totalProcessed = 0;
            int totalDeleted = 0;
            int totalFailed = 0;
            int currentOffset = 0;

            while (currentOffset < totalCount) {
                long batchStartTime = System.currentTimeMillis();

                // 查询当前批次需要删除的ID列表
                List<Long> idsToDelete = userShiftConfigAdapter.selectMigratedDataIdsForRollback(
                        punchClassConfigIds, startDayId, endDayId, taskFlagPrefix, currentOffset, batchSize);

                if (CollectionUtils.isEmpty(idsToDelete)) {
                    log.info("当前批次查询结果为空，结束回滚, currentOffset: {}", currentOffset);
                    break;
                }

                // 执行批量物理删除
                RollbackBatchResult batchResult = processBatchRollback(idsToDelete);
                totalProcessed += idsToDelete.size();
                totalDeleted += batchResult.getDeletedCount();
                totalFailed += batchResult.getFailedCount();

                long batchEndTime = System.currentTimeMillis();

                log.info("回滚批次处理完成, country: {}, 当前批次: {}, 本批处理: {}, 本批删除: {}, 本批失败: {}, 累计处理: {}, 处理耗时: {}ms",
                        country, (currentOffset / batchSize) + 1, idsToDelete.size(),
                        batchResult.getDeletedCount(), batchResult.getFailedCount(),
                        totalProcessed, batchEndTime - batchStartTime);
                XxlJobLogger.log("回滚批次处理完成, country: {}, 当前批次: {}, 本批处理: {}, 本批删除: {}, 本批失败: {}, 累计处理: {}, 处理耗时: {}ms",
                        country, (currentOffset / batchSize) + 1, idsToDelete.size(),
                        batchResult.getDeletedCount(), batchResult.getFailedCount(),
                        totalProcessed, batchEndTime - batchStartTime);

                currentOffset += batchSize;
            }

            long endTime = System.currentTimeMillis();
            log.info("回滚操作完成, country: {}, 总计处理: {}, 成功删除: {}, 失败: {}, 总耗时: {}ms",
                    country, totalProcessed, totalDeleted, totalFailed, endTime - startTime);
            XxlJobLogger.log("回滚操作完成, country: {}, 总计处理: {}, 成功删除: {}, 失败: {}, 总耗时: {}ms",
                    country, totalProcessed, totalDeleted, totalFailed, endTime - startTime);

            // 如果有失败的记录，返回false
            return totalFailed == 0;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("回滚操作失败, country: {}, dayId范围: {} - {}, 耗时: {}ms",
                    country, startDayId, endDayId, endTime - startTime, e);
            XxlJobLogger.log("回滚操作失败, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }

    /**
     * 根据考勤组ID列表获取对应的班次配置ID列表
     * 通过映射表查询 HR 考勤组ID 对应的新系统班次配置ID
     *
     * @param punchConfigIds 考勤组ID列表
     * @return 班次配置ID列表
     */
    private List<Long> getPunchClassConfigIdsByPunchConfigIds(List<Long> punchConfigIds) {
        if (CollectionUtils.isEmpty(punchConfigIds)) {
            log.debug("获取班次配置ID失败，考勤组ID列表为空");
            return Collections.emptyList();
        }

        try {
            // 查询映射表获取班次配置ID
            List<Long> punchClassConfigIds = mappingPunchClassConfigService.getPunchClassConfigIdsByHrPunchConfigIds(punchConfigIds);

            log.debug("获取班次配置ID完成, 输入考勤组数量: {}, 输出班次配置数量: {}",
                    punchConfigIds.size(), punchClassConfigIds != null ? punchClassConfigIds.size() : 0);

            return punchClassConfigIds != null ? punchClassConfigIds : Collections.emptyList();

        } catch (Exception e) {
            log.error("获取班次配置ID失败, punchConfigIds: {}", punchConfigIds, e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理单批次回滚删除操作
     * 包含错误恢复机制，单批失败不影响整体流程
     *
     * @param idsToDelete 需要删除的ID列表
     * @return 批次处理结果
     */
    private RollbackBatchResult processBatchRollback(List<Long> idsToDelete) {
        if (CollectionUtils.isEmpty(idsToDelete)) {
            log.debug("批次回滚处理: ID列表为空");
            return new RollbackBatchResult(0, 0);
        }
        // 执行批量物理删除
        int deletedCount = userShiftConfigAdapter.delete(idsToDelete);
        log.info("批次回滚删除成功, 预期删除: {}, 实际删除: {}",
                idsToDelete.size(), deletedCount);
        return new RollbackBatchResult(deletedCount, 0);
    }

    /**
     * 迁移结果内部类
     */
    @Data
    public static class MigrationResult {
        private final int successCount;
        private final int failedCount;
        
        public MigrationResult(int successCount, int failedCount) {
            this.successCount = successCount;
            this.failedCount = failedCount;
        }

    }

    /**
     * 回滚批次处理结果
     */
    @Data
    @AllArgsConstructor
    public static class RollbackBatchResult {
        /**
         * 成功删除的记录数
         */
        private int deletedCount;

        /**
         * 失败的记录数
         */
        private int failedCount;
    }



}
