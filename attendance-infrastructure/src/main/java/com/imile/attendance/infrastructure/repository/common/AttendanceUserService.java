package com.imile.attendance.infrastructure.repository.common;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.common.CommonUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.mapstruct.CommonMapstruct;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.DriverQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveQuery;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.genesis.api.enums.WorkStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21
 * @Description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AttendanceUserService {

    private final int BATCH_SIZE = 1000;
    private static final int TIMEOUT_SECONDS = 60;

    private static final Executor USER_QUERY_EXECUTOR = new ThreadPoolExecutor(
            6,
            8,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1),
            new ThreadFactoryBuilder()
                    .setNameFormat("user-query-pool-%d")
                    .setDaemon(true)
                    .build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    private final UserInfoDao userInfoDao;

    /**
     * 抽象异步批处理方法
     *
     * @param userIds       需要查询的用户ID列表
     * @param queryFunction 具体的查询函数
     * @return 查询结果
     */
    private <T> List<T> executeAsyncBatchProcess(List<Long> userIds, Function<List<Long>, List<T>> queryFunction) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        List<List<Long>> batches = Lists.partition(userIds, BATCH_SIZE);
        if (batches.size() == 1) {
            return queryFunction.apply(batches.get(0));
        }
        List<CompletableFuture<List<T>>> futures = batches.stream()
                .map(batchIds -> CompletableFuture.supplyAsync(
                        () -> queryFunction.apply(batchIds),
                        USER_QUERY_EXECUTOR).exceptionally(ex -> {
                            log.error("Failed to process batch: {}", batchIds, ex);
                            return Collections.emptyList();
                        }))
                .collect(Collectors.toList());

        try {
            return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream()
                            .map(CompletableFuture::join)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList()))
                    .get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            log.error("Error during async batch processing, falling back to sync processing", e);
            // 使用流式API进行分批处理作为回退策略
            return batches.stream()
                    .flatMap(batch -> queryFunction.apply(batch).stream())
                    .collect(Collectors.toList());
        }
    }

    /**
     * 记录性能日志的查询执行器
     */
    private <T> List<T> executeWithPerformanceLogging(List<Long> userIds, String operation,
            Supplier<List<T>> queryFunction) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        long startTime = System.currentTimeMillis();
        try {
            List<T> results = queryFunction.get();
            log.info("{} query for {} ids took {}ms, returned {} results",
                    operation, userIds.size(), System.currentTimeMillis() - startTime, results.size());
            return results;
        } catch (Exception e) {
            log.error("{} query failed for {} ids after {}ms",
                    operation, userIds.size(), System.currentTimeMillis() - startTime, e);
            throw e;
        }
    }

    /**
     * 查询在职用户
     */
    public List<AttendanceUser> listOnJobUsers(List<Long> userIds) {
        return executeWithPerformanceLogging(userIds, "listOnJobUsers", () -> {
            UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                    .userIds(userIds)
                    .workStatus(WorkStatusEnum.ON_JOB.getCode())
                    .status(StatusEnum.ACTIVE.getCode())
                    .build();
            return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
        });
    }

    /**
     * 查询国家下在职的非司机用户（进入考勤规则配置的用户）
     */
    public List<AttendanceUser> listOnJobNoDriverUsers(String country) {
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .locationCountry(country)
                .isDriver(IsDeleteEnum.NO.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    /**
     * 异步查询在职用户
     */
    public List<AttendanceUser> asyncListOnJobUsers(List<Long> userIds) {
        return executeAsyncBatchProcess(userIds, this::listOnJobUsers);
    }

    /**
     * 查询活跃且在职的用户
     */
    public List<AttendanceUser> listActiveAndOnJobUsers(List<Long> userIds) {
        return executeWithPerformanceLogging(userIds, "listActiveAndOnJobUsers", () -> {
            UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                    .userIds(userIds)
                    .status(StatusEnum.ACTIVE.getCode())
                    .workStatus(WorkStatusEnum.ON_JOB.getCode())
                    .build();
            return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
        });
    }

    /**
     * 异步查询活跃且在职的用户
     */
    public List<AttendanceUser> asyncListActiveAndOnJobUsers(List<Long> userIds) {
        return executeAsyncBatchProcess(userIds, this::listActiveAndOnJobUsers);
    }

    public AttendanceUser getByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.getById(userId));
    }

    /**
     * 根据用户编码查询用户信息（带缓存）
     * 使用远程缓存，提升查询性能
     */
//    @Cached(name = Constants.CacheKey.USER_CACHE_KEY,
//            key = "#userCode",
//            cacheType = CacheType.REMOTE,
//            expire = 15,
//            timeUnit = TimeUnit.MINUTES
//    )
    public AttendanceUser getByUserCodeCache(String userCode) {
        long startTime = System.currentTimeMillis();
        try {
            AttendanceUser result = getByUserCode(userCode);
            log.debug("缓存查询用户信息, userCode: {}, found: {}, 耗时: {}ms",
                    userCode, result != null, System.currentTimeMillis() - startTime);
            return result;
        } catch (Exception e) {
            log.error("缓存查询用户信息失败, userCode: {}, 耗时: {}ms",
                    userCode, System.currentTimeMillis() - startTime, e);
            throw e;
        }
    }

    /**
     * 删除用户缓存
     */
//    @CacheInvalidate(name = Constants.CacheKey.USER_CACHE_KEY, key = "#userCode")
    public void invalidateUserCache(String userCode) {
        log.info("删除用户缓存, userCode: {}", userCode);
    }


    public AttendanceUser getByUserCode(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            return null;
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userCode(userCode)
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<UserInfoDO> userInfoDOS = userInfoDao.userList(userDaoQuery);
        if (CollectionUtils.isEmpty(userInfoDOS)) {
            return null;
        }
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDOS.get(0));
    }

    /**
     * 批量根据用户编码查询在职用户
     */
    public List<AttendanceUser> listOnJobNonDriverUserByCodes(List<String> userCodes) {
        if (CollectionUtils.isEmpty(userCodes)) {
            return Collections.emptyList();
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userCodes(userCodes)
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .status(StatusEnum.ACTIVE.getCode())
                .isDriver(BusinessConstant.N)
                .build();
        List<UserInfoDO> userInfoDOS = userInfoDao.userList(userDaoQuery);
        if (CollectionUtils.isEmpty(userInfoDOS)) {
            return null;
        }
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDOS);
    }

    public List<AttendanceUser> listUsersByIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userIds(userIds)
                .build();
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    public List<AttendanceUser> listUsersByIds(Set<Long> userIdSet) {
        if (CollectionUtils.isEmpty(userIdSet)) {
            return Collections.emptyList();
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userIds(new ArrayList<>(userIdSet))
                .build();
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    public List<AttendanceUser> listUsersByUserCodes(List<String> userCodes) {
        if (CollectionUtils.isEmpty(userCodes)) {
            return Collections.emptyList();
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userCodes(userCodes)
                .build();
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    /**
     * 查询部门下在职的非司机用户
     */
    public List<AttendanceUser> listOnJobUserByDeptIdList(List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .deptIds(deptIds)
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .build();
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    /**
     * 查询国家下在职的非司机用户（进入考勤规则配置的用户）
     */
    public List<AttendanceUser> listOnJobNonDriverUserByDeptIdList(List<Long> deptIds, String locationCountry) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .deptIds(deptIds)
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(IsDeleteEnum.NO.getCode())
                .locationCountry(locationCountry)
                .employeeTypeList(CommonUserService.getCountryEmployeeTypes(locationCountry))
                .build();
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    /**
     * 查询国家列表下的在职的非司机用户
     */
    public List<AttendanceUser> listOnJobNonDriverUserByCountries(List<String> locationCountries,List<Long> deptIdList) {
        if (CollectionUtils.isEmpty(locationCountries)) {
            return Collections.emptyList();
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(IsDeleteEnum.NO.getCode())
                .locationCountryList(locationCountries)
                .deptIds(deptIdList)
                .build();
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    public List<AttendanceUser> listUsersByLocationCountry(String locationCountry) {
        if (StringUtils.isEmpty(locationCountry)) {
            return Collections.emptyList();
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .locationCountry(locationCountry)
                .build();
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    public List<AttendanceUser> listUsersByQuery(UserDaoQuery userDaoQuery) {
        if (null == userDaoQuery) {
            return Collections.emptyList();
        }
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    public List<AttendanceUser> transferToUser(List<UserInfoDO> list) {
        return CommonMapstruct.INSTANCE.mapToUser(list);
    }

    /**
     * 查询司机接口
     *
     * @param driverQuery 查询条件
     * @return List<HrmsUserInfoDO>
     */
    public List<AttendanceUser> queryDriver(DriverQuery driverQuery) {
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.queryDriver(driverQuery));
    }

    public List<AttendanceUser> selectUserForZkteco(List<Long> deptIdList, List<Long> userIdList) {
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.selectUserForZkteco(deptIdList, userIdList));
    }

    public List<AttendanceUser> listByUserCodes(List<String> userCodes) {
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userCodes(userCodes)
                .build();
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.userList(userDaoQuery));
    }

    /**
     * 假期关联用户查询
     * @param userLeaveQuery
     * @return
     */
    public List<AttendanceUser> selectLeaveUser(UserLeaveQuery userLeaveQuery) {
        return CommonMapstruct.INSTANCE.mapToUser(userInfoDao.selectLeaveUser(userLeaveQuery));
    }




}
