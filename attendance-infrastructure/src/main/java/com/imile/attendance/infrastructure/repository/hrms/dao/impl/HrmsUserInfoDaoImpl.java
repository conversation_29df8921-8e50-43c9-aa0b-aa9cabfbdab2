package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsUserInfoMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
@Slf4j
@Component
@DS(Constants.TableSchema.hrms)
@RequiredArgsConstructor
public class HrmsUserInfoDaoImpl extends ServiceImpl<HrmsUserInfoMapper, HrmsUserInfoDO> implements HrmsUserInfoDao {


    @Override
    public HrmsUserInfoDO getByCode(String userCode) {
        LambdaQueryWrapper<HrmsUserInfoDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserInfoDO.class)
                .eq(HrmsUserInfoDO::getUserCode, userCode)
                .eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return getOne(queryWrapper);
    }

    @Override
    public List<HrmsUserInfoDO> listByCodes(List<String> userCodes) {
        if (CollectionUtils.isEmpty(userCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsUserInfoDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserInfoDO.class)
                .in(HrmsUserInfoDO::getUserCode, userCodes)
                .eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<HrmsUserInfoDO> listByPage(int currentPage, int pageSize) {
        PageInfo<HrmsUserInfoDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }

    @Override
    public HrmsUserInfoDO getByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        return getById(userId);
    }

    @Override
//    @Cached(name = Constants.CacheKey.HRMS_USER_CACHE_KEY,
//            key = "#userCode",
//            cacheType = CacheType.REMOTE,
//            expire = 15,
//            timeUnit = TimeUnit.MINUTES
//    )
    public HrmsUserInfoDO getByUserCodeCache(String userCode) {
        long startTime = System.currentTimeMillis();
        try {
            HrmsUserInfoDO result = getByCode(userCode);
            log.debug("缓存查询用户信息, userCode: {}, found: {}, 耗时: {}ms",
                    userCode, result != null, System.currentTimeMillis() - startTime);
            return result;
        } catch (Exception e) {
            log.error("缓存查询用户信息失败, userCode: {}, 耗时: {}ms",
                    userCode, System.currentTimeMillis() - startTime, e);
            throw e;
        }
    }

    @Override
    public List<HrmsUserInfoDO> listUsersByIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return listByIds(userIds);
    }
}
