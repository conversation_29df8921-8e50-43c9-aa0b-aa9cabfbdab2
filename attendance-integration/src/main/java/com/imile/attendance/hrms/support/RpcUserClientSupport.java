package com.imile.attendance.hrms.support;

import com.imile.attendance.hrms.RpcUserClient;
import com.imile.attendance.hrms.dto.UserDynamicInfoRpcDTO;
import com.imile.attendance.hrms.mapstruct.RpcUserClientMapstruct;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/08/05
 * @Time 19:11
 * @Description
 */
@Component
public class RpcUserClientSupport {

    @Resource
    private RpcUserClient rpcUserClient;


    public UserDynamicInfoRpcDTO getUserDynamicInfoById(Long userId) {
        if (userId == null) {
            return null;
        }
        List<UserDynamicInfoRpcDTO> userDynamicInfoRpcDTOList = listUserDynamicInfoById(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(userDynamicInfoRpcDTOList)) {
            return null;
        }
        return userDynamicInfoRpcDTOList.get(0);
    }


    public UserDynamicInfoRpcDTO getUserDynamicInfoByCode(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            return null;
        }
        List<UserDynamicInfoRpcDTO> userDynamicInfoRpcDTOList = listUserDynamicInfo(Collections.singletonList(userCode));
        if (CollectionUtils.isEmpty(userDynamicInfoRpcDTOList)) {
            return null;
        }
        return userDynamicInfoRpcDTOList.get(0);
    }


    public List<UserDynamicInfoRpcDTO> listUserDynamicInfo(List<String> userCodeList){
        List<UserDynamicInfoDTO> userDynamicInfoDTOS = rpcUserClient.listUserDynamicInfo(userCodeList,
                Arrays.asList(UserDynamicFieldEnum.LOCATION_COUNTRY,
                        UserDynamicFieldEnum.LOCATION_PROVINCE,
                        UserDynamicFieldEnum.LOCATION_CITY,
                        UserDynamicFieldEnum.DEPT_CODE));
        return RpcUserClientMapstruct.INSTANCE.mapToUserDynamicInfoRpcDTO(userDynamicInfoDTOS);
    }


    /**
     * 根据人员ID批量获取人员动态业务信息
     *
     * @param idList 人员ID列表（一次性不能超过1000，自行分批）
     * @return List<UserDynamicInfoDTO>
     */
    public List<UserDynamicInfoRpcDTO> listUserDynamicInfoById(List<Long> idList) {
        List<UserDynamicInfoDTO> userDynamicInfoDTOList = rpcUserClient.listUserDynamicInfoById(idList, Arrays.asList(UserDynamicFieldEnum.LOCATION_COUNTRY,
                UserDynamicFieldEnum.LOCATION_PROVINCE,
                UserDynamicFieldEnum.LOCATION_CITY,
                UserDynamicFieldEnum.DEPT_CODE));
        return RpcUserClientMapstruct.INSTANCE.mapToUserDynamicInfoRpcDTO(userDynamicInfoDTOList);
    }

}
