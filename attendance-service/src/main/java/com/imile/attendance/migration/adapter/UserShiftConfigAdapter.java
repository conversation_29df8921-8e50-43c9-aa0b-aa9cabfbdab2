package com.imile.attendance.migration.adapter;

import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dao.migrate.UserShiftConfigMigrateDao;
import com.imile.attendance.infrastructure.repository.shift.dto.DayShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.mapper.UserShiftConfigMapper;
import com.imile.attendance.infrastructure.repository.shift.mapper.migrate.UserShiftConfigMigrateMapper;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.migrate.UserShiftConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.AbstractPairAdapter;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工排班配置适配器
 * 支持在原始表和migrate表之间透明切换的排班配置DAO操作
 * 
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Slf4j
@Component
public class UserShiftConfigAdapter extends AbstractPairAdapter<UserShiftConfigMigrateDO, UserShiftConfigDO> {

    @Resource
    private UserShiftConfigMigrateDao userShiftConfigMigrateDao;
    @Resource
    private UserShiftConfigMigrateMapper userShiftConfigMigrateMapper;
    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private UserShiftConfigMapper userShiftConfigMapper;

    public UserShiftConfigAdapter(List<DataConverter<UserShiftConfigMigrateDO, UserShiftConfigDO>> dataConverters) {
        super(dataConverters);
    }

    /**
     * 保存单个排班配置
     * 根据配置自动选择使用原始表还是migrate表
     * 
     * @param userShiftConfigDO 排班配置DO对象
     */
    public void saveOne(UserShiftConfigDO userShiftConfigDO) {
        log.info("保存排班配置, configId: {}, userId: {}, dayId: {}, 当前使用{}表", 
                userShiftConfigDO.getId(), userShiftConfigDO.getUserId(), userShiftConfigDO.getDayId(),
                isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateOneWrapper(
                userShiftConfigDO,
                migrateDO -> {
                    // 保存到migrate表
                    userShiftConfigMigrateDao.save(migrateDO);
                    log.debug("排班配置已保存到migrate表, configId: {}", migrateDO.getId());
                },
                // 保存到原始表
                realDO -> {
                    userShiftConfigDao.save(realDO);
                    log.debug("排班配置已保存到原始表, configId: {}", realDO.getId());
                }
        );
    }

    /**
     * 批量保存排班配置
     * 支持大批量数据的高效保存操作
     * 
     * @param userShiftConfigDOList 排班配置DO列表
     * @return 保存是否成功
     */
    public boolean saveBatch(List<UserShiftConfigDO> userShiftConfigDOList) {
        log.info("批量保存排班配置, 数量: {}, 当前使用{}表", 
                userShiftConfigDOList != null ? userShiftConfigDOList.size() : 0,
                isShouldUseMigrate() ? "migrate" : "原始");
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            log.warn("批量保存排班配置: 输入列表为空");
            return false;
        }

        return commonWriteWithResult(
                () -> {
                    // 转换并批量保存到migrate表
                    List<UserShiftConfigMigrateDO> migrateDOList = userShiftConfigDOList.stream()
                            .map(getConverter()::convertFromReal)
                            .collect(java.util.stream.Collectors.toList());
                    Integer result = userShiftConfigMigrateMapper.insertBatchSomeColumn(migrateDOList);
                    log.debug("排班配置批量保存到migrate表结果: {}, 数量: {}", result, migrateDOList.size());
                    return true;
                },
                () -> {
                    // 批量保存到原始表
                    Integer result = userShiftConfigMapper.insertBatchSomeColumn(userShiftConfigDOList);
                    log.debug("排班配置批量保存到原始表结果: {}, 数量: {}", result, userShiftConfigDOList.size());
                    return true;
                }
        );
    }

    /**
     * 批量更新排班配置
     * 支持大批量数据的高效更新操作
     * 
     * @param userShiftConfigDOList 排班配置DO列表
     * @return 更新是否成功
     */
    public boolean updateBatchById(List<UserShiftConfigDO> userShiftConfigDOList) {
        log.info("批量更新排班配置, 数量: {}, 当前使用{}表", 
                userShiftConfigDOList != null ? userShiftConfigDOList.size() : 0,
                isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 转换并批量更新migrate表
                    List<UserShiftConfigMigrateDO> migrateDOList = userShiftConfigDOList.stream()
                            .map(getConverter()::convertFromReal)
                            .collect(java.util.stream.Collectors.toList());
                    Integer result = userShiftConfigMigrateMapper.replaceIntoBatchSomeColumn(migrateDOList);
                    log.debug("排班配置批量更新到migrate表结果: {}, 数量: {}", result, migrateDOList.size());
                    return true;
                },
                () -> {
                    // 批量更新原始表
                    Integer result = userShiftConfigMapper.replaceIntoBatchSomeColumn(userShiftConfigDOList);
                    log.debug("排班配置批量更新到原始表结果: {}, 数量: {}", result, userShiftConfigDOList.size());
                    return true;
                }
        );
    }

    /**
     * 根据ID查询排班配置
     *
     * @param id 配置ID
     * @return 排班配置（转换为原始DO类型）
     */
    public UserShiftConfigDO getById(Long id) {
        log.debug("根据ID查询排班配置, id: {}, 当前使用{}表",
                id, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readWrapper(
                () -> userShiftConfigMigrateDao.getById(id),
                () -> userShiftConfigDao.getById(id)
        );
    }

    /**
     * 根据ID更新排班配置
     *
     * @param userShiftConfigDO 排班配置DO对象
     * @return 更新是否成功
     */
    public boolean updateById(UserShiftConfigDO userShiftConfigDO) {
        log.info("根据ID更新排班配置, configId: {}, 当前使用{}表",
                userShiftConfigDO.getId(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 转换并更新migrate表
                    UserShiftConfigMigrateDO migrateDO = getConverter().convertFromReal(userShiftConfigDO);
                    boolean result = userShiftConfigMigrateDao.updateById(migrateDO);
                    log.debug("排班配置更新到migrate表结果: {}, configId: {}", result, migrateDO.getId());
                    return result;
                },
                () -> {
                    // 更新原始表
                    boolean result = userShiftConfigDao.updateById(userShiftConfigDO);
                    log.debug("排班配置更新到原始表结果: {}, configId: {}", result, userShiftConfigDO.getId());
                    return result;
                }
        );
    }

    /**
     * 根据ID物理删除排班配置
     *
     * @param id 配置ID
     * @return 删除是否成功
     */
    public boolean removeById(Long id) {
        log.info("物理删除排班配置, id: {}, 当前使用{}表",
                id, isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 物理删除migrate表记录
                    boolean result = userShiftConfigMigrateDao.removeById(id);
                    log.info("排班配置从migrate表物理删除{}, id: {}", result ? "成功" : "失败", id);
                    return result;
                },
                () -> {
                    // 物理删除原始表记录
                    boolean result = userShiftConfigDao.removeById(id);
                    log.info("排班配置从原始表物理删除{}, id: {}", result ? "成功" : "失败", id);
                    return result;
                }
        );
    }

    /**
     * 查找用户排班信息
     *
     * @param userId 用户ID
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    public List<UserShiftConfigDO> selectUserShift(Long userId, Long startDayId, Long endDayId) {
        log.debug("查找用户排班信息, userId: {}, dayId范围: {} - {}, 当前使用{}表",
                userId, startDayId, endDayId, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readBatchWrapper(
                () -> userShiftConfigMigrateDao.selectUserShift(userId, startDayId, endDayId),
                () -> userShiftConfigDao.selectUserShift(userId, startDayId, endDayId)
        );
    }

    /**
     * 获取用户在指定天数的排班数据
     *
     * @param userId 用户ID
     * @param dayIds 日期ID列表
     * @return 排班记录列表
     */
    public List<UserShiftConfigDO> selectUserShiftByDayIds(Long userId, List<Long> dayIds) {
        log.debug("获取用户指定天数排班数据, userId: {}, dayIds数量: {}, 当前使用{}表",
                userId, dayIds != null ? dayIds.size() : 0, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readBatchWrapper(
                () -> userShiftConfigMigrateDao.selectUserShiftByDayIds(userId, dayIds),
                () -> userShiftConfigDao.selectUserShiftByDayIds(userId, dayIds)
        );
    }

    /**
     * 逻辑删除排班信息
     *
     * @param dayShiftConfigDTOList 待删除的排班配置列表
     */
    public void updateToDelete(List<DayShiftConfigDTO> dayShiftConfigDTOList) {
        log.info("逻辑删除排班信息, 数量: {}, 当前使用{}表",
                dayShiftConfigDTOList != null ? dayShiftConfigDTOList.size() : 0,
                isShouldUseMigrate() ? "migrate" : "原始");

        commonWrite(
                () -> userShiftConfigMigrateDao.updateToDelete(dayShiftConfigDTOList),
                () -> userShiftConfigDao.updateToDelete(dayShiftConfigDTOList)
        );
    }

    /**
     * 物理删除排班信息
     *
     * @param ids 待删除的记录ID列表
     * @return 删除的记录数量
     */
    public int delete(List<Long> ids) {
        log.info("物理删除排班信息, ids数量: {}, 当前使用{}表",
                ids != null ? ids.size() : 0, isShouldUseMigrate() ? "migrate" : "原始");

        return commonQuery(
                () -> userShiftConfigMigrateDao.delete(ids),
                () -> userShiftConfigDao.delete(ids)
        );
    }

    /**
     * 获取用户规定时间段内的排班信息
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始日期ID
     * @param endDayId 结束日期ID
     * @return 排班记录列表
     */
    public List<UserShiftConfigDO> selectRecordByUserIdList(List<Long> userIdList, Long startDayId, Long endDayId) {
        log.debug("获取用户列表排班信息, userIds数量: {}, dayId范围: {} - {}, 当前使用{}表",
                userIdList != null ? userIdList.size() : 0, startDayId, endDayId,
                isShouldUseMigrate() ? "migrate" : "原始");

        return super.readBatchWrapper(
                () -> userShiftConfigMigrateDao.selectRecordByUserIdList(userIdList, startDayId, endDayId),
                () -> userShiftConfigDao.selectRecordByUserIdList(userIdList, startDayId, endDayId)
        );
    }

    /**
     * 统计需要回滚的迁移数据数量
     * 根据班次配置ID列表、日期范围和任务标识统计符合回滚条件的记录数量
     *
     * @param punchClassConfigIds 班次配置ID列表
     * @param startDayId 开始日期ID（可选）
     * @param endDayId 结束日期ID（可选）
     * @param taskFlagPrefix 任务标识前缀，用于识别迁移数据
     * @return 符合条件的记录数量
     */
    public Long countMigratedDataForRollback(List<Long> punchClassConfigIds, Long startDayId, Long endDayId, String taskFlagPrefix) {
        return userShiftConfigDao.countMigratedDataForRollback(punchClassConfigIds, startDayId, endDayId, taskFlagPrefix);
    }

    /**
     * 分页查询需要回滚的迁移数据ID列表
     * 根据班次配置ID列表、日期范围和任务标识查询符合回滚条件的记录ID，支持分页处理
     *
     * @param punchClassConfigIds 班次配置ID列表
     * @param startDayId 开始日期ID（可选）
     * @param endDayId 结束日期ID（可选）
     * @param taskFlagPrefix 任务标识前缀，用于识别迁移数据
     * @param offset 分页偏移量
     * @param limit 分页大小
     * @return 符合条件的记录ID列表
     */
    public List<Long> selectMigratedDataIdsForRollback(List<Long> punchClassConfigIds, Long startDayId, Long endDayId,
                                                       String taskFlagPrefix, Integer offset, Integer limit) {
        return userShiftConfigDao.selectMigratedDataIdsForRollback(punchClassConfigIds, startDayId, endDayId, taskFlagPrefix, offset, limit);
    }

}
