package com.imile.attendance.cycleConfig.util;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleCalendarDataDTO;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleCalendarDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 考勤周期日历计算工具类
 * 
 * 负责根据考勤周期配置计算具体的周期开始和结束日期，
 * 参考 AttendanceMonthReportService#buildAttendanceCycleDTO 的计算逻辑
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Slf4j
@Component
public class AttendanceCycleCalendarCalculator {

    /**
     * 默认维护的年份跨度（去年、今年、后年）
     */
    private static final int YEAR_SPAN = 3;
    
    /**
     * 当前年份偏移量：-1表示去年，0表示今年，1表示后年
     */
    private static final int[] YEAR_OFFSETS = {-1, 0, 1};

    /**
     * 为指定的考勤周期配置生成日历数据
     *
     * @param cycleConfig 考勤周期配置
     * @return 生成的日历数据列表
     */
    public List<AttendanceCycleCalendarDO> generateCalendarData(AttendanceCycleConfigDO cycleConfig) {
        if (cycleConfig == null || cycleConfig.getCountry() == null) {
            log.warn("考勤周期配置为空或国家为空，跳过日历数据生成");
            return Collections.emptyList();
        }

        log.info("开始为国家 {} 生成考勤周期日历数据，周期类型：{}", 
                cycleConfig.getCountry(), cycleConfig.getCycleType());

        List<AttendanceCycleCalendarDO> calendarDataList = new ArrayList<>();
        int currentYear = DateUtil.year(new Date());

        // 生成3年跨度的日历数据
        for (int yearOffset : YEAR_OFFSETS) {
            int targetYear = currentYear + yearOffset;
            List<AttendanceCycleCalendarDO> yearlyData = generateYearlyCalendarData(cycleConfig, targetYear);
            calendarDataList.addAll(yearlyData);
        }

        log.info("为国家 {} 生成了 {} 条日历数据", cycleConfig.getCountry(), calendarDataList.size());
        return calendarDataList;
    }

    /**
     * 生成指定年份的日历数据
     *
     * @param cycleConfig 考勤周期配置
     * @param year 目标年份
     * @return 该年份的日历数据列表
     */
    private List<AttendanceCycleCalendarDO> generateYearlyCalendarData(AttendanceCycleConfigDO cycleConfig, int year) {
        List<AttendanceCycleCalendarDO> yearlyData = new ArrayList<>();

        // 根据周期类型生成数据
        if (AttendanceCycleTypeEnum.MONTH.getType().equals(cycleConfig.getCycleType())) {
            // 月度周期：为每个月生成一条记录
            for (int month = 1; month <= 12; month++) {
                AttendanceCycleCalendarDO calendarDO = calculateMonthlyCycle(cycleConfig, year, month);
                if (calendarDO != null) {
                    yearlyData.add(calendarDO);
                }
            }
        } else if (AttendanceCycleTypeEnum.WEEK.getType().equals(cycleConfig.getCycleType())) {
            // 周度周期：生成该年的周度数据
            yearlyData.addAll(calculateWeeklyCycles(cycleConfig, year));
        }

        return yearlyData;
    }

    /**
     * 计算月度周期的开始和结束日期
     * 参考 AttendanceMonthReportService#buildAttendanceCycleDTO 的逻辑
     *
     * @param cycleConfig 考勤周期配置
     * @param year 年份
     * @param month 月份
     * @return 日历数据对象
     */
    private AttendanceCycleCalendarDO calculateMonthlyCycle(AttendanceCycleConfigDO cycleConfig, int year, int month) {
        try {
            String cycleStart = cycleConfig.getCycleStart();
            String cycleEnd = cycleConfig.getCycleEnd();
            
            Date cycleStartDate;
            Date cycleEndDate;

            String cycleMonth = String.format("%04d%02d", year, month);
            
            if (CycleTypeEnum.END_OF_MONTH_CODE.equals(cycleEnd)) {
                // 整月周期：从当月第一天到当月最后一天
                cycleStartDate = DateUtil.beginOfMonth(DateUtil.parse(cycleMonth, "yyyyMM"));
                cycleEndDate = DateUtil.endOfMonth(DateUtil.parse(cycleMonth, "yyyyMM"));
            } else {
                // 自定义日期周期：从上月的cycleStart到当月的cycleEnd
                cycleStartDate = DateUtil.offsetMonth(
                    DateUtil.parse(cycleMonth + cycleStart, "yyyyMMdd"), -1);
                cycleEndDate = DateUtil.parse(
                    DateUtil.format(DateUtil.offsetMonth(cycleStartDate, 1), "yyyyMM") + cycleEnd, "yyyyMMdd");
            }

            // 创建日历数据对象
            AttendanceCycleCalendarDO calendarDO = new AttendanceCycleCalendarDO();
            calendarDO.setCountry(cycleConfig.getCountry());
            calendarDO.setAttendanceYear(year);
            calendarDO.setAttendanceMonth(month);
            calendarDO.setCycleStartDate(cycleStartDate);
            calendarDO.setCycleEndDate(cycleEndDate);

            return calendarDO;
            
        } catch (Exception e) {
            log.error("计算月度周期失败，国家：{}，年月：{}-{}，错误：{}", 
                    cycleConfig.getCountry(), year, month, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 计算周度周期数据
     *
     * @param cycleConfig 考勤周期配置
     * @param year 年份
     * @return 该年份的周度日历数据列表
     */
    private List<AttendanceCycleCalendarDO> calculateWeeklyCycles(AttendanceCycleConfigDO cycleConfig, int year) {
        List<AttendanceCycleCalendarDO> weeklyData = new ArrayList<>();
        
        try {
            // 获取该年第一天和最后一天
            Date yearStart = DateUtil.beginOfYear(DateUtil.parse(String.valueOf(year), "yyyy"));
            Date yearEnd = DateUtil.endOfYear(yearStart);
            
            // 按周遍历整年
            Date currentWeekStart = yearStart;
            int weekNumber = 1;
            
            while (currentWeekStart.before(yearEnd)) {
                Date weekStartDate = CycleTypeEnum.WEEK.getCycleDate(currentWeekStart, cycleConfig.getCycleStart(), 0);
                Date weekEndDate = CycleTypeEnum.WEEK.getCycleDate(currentWeekStart, cycleConfig.getCycleEnd(), 0);
                
                AttendanceCycleCalendarDO calendarDO = new AttendanceCycleCalendarDO();
                calendarDO.setCountry(cycleConfig.getCountry());
                calendarDO.setAttendanceYear(year);
                calendarDO.setAttendanceMonth(weekNumber); // 对于周度，使用周数作为月份字段
                calendarDO.setCycleStartDate(weekStartDate);
                calendarDO.setCycleEndDate(weekEndDate);
                
                weeklyData.add(calendarDO);
                
                // 移动到下一周
                currentWeekStart = DateUtil.offsetWeek(currentWeekStart, 1);
                weekNumber++;
            }
            
        } catch (Exception e) {
            log.error("计算周度周期失败，国家：{}，年份：{}，错误：{}", 
                    cycleConfig.getCountry(), year, e.getMessage(), e);
        }
        
        return weeklyData;
    }

    /**
     * 检查是否需要处理跨年场景
     *
     * @param cycleStartDate 周期开始日期
     * @param cycleEndDate 周期结束日期
     * @return 是否跨年
     */
    public boolean isCrossYearScenario(Date cycleStartDate, Date cycleEndDate) {
        return DateUtil.year(cycleStartDate) != DateUtil.year(cycleEndDate);
    }

    /**
     * 验证日历数据的有效性
     *
     * @param calendarDO 日历数据对象
     * @return 是否有效
     */
    public boolean validateCalendarData(AttendanceCycleCalendarDO calendarDO) {
        if (calendarDO == null) {
            return false;
        }
        
        return calendarDO.getCountry() != null 
                && calendarDO.getAttendanceYear() != null 
                && calendarDO.getAttendanceMonth() != null
                && calendarDO.getCycleStartDate() != null 
                && calendarDO.getCycleEndDate() != null
                && calendarDO.getCycleStartDate().before(calendarDO.getCycleEndDate());
    }
}
