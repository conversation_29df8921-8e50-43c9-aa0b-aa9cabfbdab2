package com.imile.attendance.migration.service;

import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.migration.dto.HrAttendanceClassConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/16
 * @Description 考勤班次配置映射服务接口
 */
public interface MappingPunchClassConfigService {

    /**
     * 保存班次配置映射
     *
     * @param mappingPunchClassConfig 班次配置映射
     * @return 是否成功
     */
    boolean save(MappingPunchClassConfigDO mappingPunchClassConfig);

    /**
     * 批量保存班次配置映射
     *
     * @param mappingPunchClassConfigs 班次配置映射列表
     * @return 是否成功
     */
    boolean batchSave(List<MappingPunchClassConfigDO> mappingPunchClassConfigs);

    /**
     * 根据HR考勤组班次ID查询班次配置映射
     *
     * @param hrPunchClassId HR考勤组班次ID
     * @return 班次配置映射
     */
    MappingPunchClassConfigDO getByHrPunchClassId(Long hrPunchClassId);

    /**
     * 根据HR考勤组ID查询班次配置映射列表
     *
     * @param hrPunchConfigId HR考勤组ID
     * @return 班次配置映射列表
     */
    List<MappingPunchClassConfigDO> listByHrPunchConfigId(Long hrPunchConfigId);

    /**
     * 根据新考勤班次配置ID查询班次配置映射
     *
     * @param punchClassConfigId 新考勤班次配置ID
     * @return 班次配置映射
     */
    MappingPunchClassConfigDO getByPunchClassConfigId(Long punchClassConfigId);

    /**
     * 根据国家查询班次配置映射列表
     *
     * @param country 国家
     * @return 班次配置映射列表
     */
    List<MappingPunchClassConfigDO> listByCountry(String country);

    /**
     * 创建HR考勤组班次配置到新考勤班次配置的映射记录
     *
     * @param groupDTO HR考勤组DTO
     * @param classConfigDTO HR班次配置DTO
     * @param punchClassConfigDO 新班次配置
     * @return 班次配置映射
     */
    MappingPunchClassConfigDO buildMapping(HrAttendanceGroupDTO groupDTO,
                                           HrAttendanceClassConfigDTO classConfigDTO,
                                           PunchClassConfigDO punchClassConfigDO);

    /**
     * 检查班次配置映射是否已存在
     *
     * @param hrPunchClassId HR考勤组班次ID
     * @return 是否存在
     */
    boolean existsMapping(Long hrPunchClassId);

    /**
     * 根据HR考勤组班次ID列表批量查询班次配置映射
     *
     * @param hrPunchClassIds HR考勤组班次ID列表
     * @return 班次配置映射列表
     */
    List<MappingPunchClassConfigDO> listByHrPunchClassIds(List<Long> hrPunchClassIds);

    /**
     * 根据HR考勤组ID列表获取对应的班次配置ID列表
     * 用于回滚操作中查询需要删除的数据
     *
     * @param hrPunchConfigIds HR考勤组ID列表
     * @return 班次配置ID列表
     */
    List<Long> getPunchClassConfigIdsByHrPunchConfigIds(List<Long> hrPunchConfigIds);
}
