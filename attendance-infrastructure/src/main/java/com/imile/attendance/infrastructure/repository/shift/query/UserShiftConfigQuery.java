package com.imile.attendance.infrastructure.repository.shift.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> chen
 * @Date 2025/4/21 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserShiftConfigQuery extends ResourceQuery {

    private String country;

    /**
     * 账号或姓名
     */
    private String userCodeOrName;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 岗位集合
     */
    private List<Long> postIdList;

    /**
     * 用工类型集合
     */
    private List<String> employeeTypeList;

    /**
     * 班次分类：多班次、固定班次
     */
    private String classNature;

    /**
     * 班次id列表（班次名称传班次id）
     */
    private List<Long> classIdList;

    /**
     * 考勤日历编码
     */
    private String attendanceConfigNo;

    /**
     * 查询起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 排班情况:已排班、未排班
     */
    private String shiftStatus;

    /**
     * 今天之后的时间
     */
    private Long nowAfterDay;

    /**
     * 考勤日
     */
    private String dayId;


    //================逻辑处理字段======================

    /**
     * 前端不需要传该参数
     */
    private List<Long> deptIds;

    /**
     * 前端不需要传递该参数
     */
    private Long userId;

    /**
     * 开始时间 前端不需要传递该参数
     */
    private Long startDayId;

    /**
     * 选中导出用
     */
    private List<Long> userIds;

    /**
     * 排班，班次等筛选条件过滤后的用户id
     */
    private List<Long> conditionUserIds;

    /**
     * conditionUserIds超过1000条的处理列表
     */
    private List<List<Long>> batchedConditionUserIds;

    /**
     * 是否需要查询特殊的国家，影响用工类型
     */
    private Boolean isNeedQuerySpecialCountry = false;

    private List<String> specialCountryList;

    private List<String> normalCountryList;

    private List<String> specialEmployeeTypeList;

    private List<String> normalEmployeeTypeList;

    private List<Long> specialDeptList;

    private List<Long> normalDeptList;
}
