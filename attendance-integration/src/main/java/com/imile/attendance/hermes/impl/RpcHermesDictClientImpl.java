package com.imile.attendance.hermes.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.hermes.RpcHermesDictClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hermes.resource.api.SysDictApi;
import com.imile.hermes.resource.dto.DictDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
@Component
@Slf4j
public class RpcHermesDictClientImpl implements RpcHermesDictClient {

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 10000)
    private SysDictApi sysDictApi;


    @Override
//    @Cached(name = Constants.CacheKey.DICT_CACHE_KEY,
//            key = "#orgId + ':' + #typeCode",
//            cacheType = CacheType.REMOTE,
//            expire = 10,
//            timeUnit = TimeUnit.MINUTES
//    )
    public List<DictDataDTO> getListRpcResult(String typeCode, Long orgId) {
        try {
            return RpcResultProcessor.process(this.sysDictApi.findDictDataByOrgIdAndType(orgId, typeCode));
        } catch (Exception e) {
            log.error("The hermes country info rpc error, ", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, List<DictDataDTO>> findDictDataByTypeCodeAndLangMap(List<String> typeCodeList, Long orgId, String lang) {
        return RpcResultProcessor.process(this.sysDictApi.findDictDataByTypeCodeAndLangMap(typeCodeList, orgId, lang));
    }

    @Override
    public Map<String, List<DictDataDTO>> findDictDataByTypeCodeMap(List<String> typeCodeList, Long orgId) {
        return RpcResultProcessor.process(this.sysDictApi.findDictDataByTypeCodeMap(typeCodeList, orgId));
    }
}
