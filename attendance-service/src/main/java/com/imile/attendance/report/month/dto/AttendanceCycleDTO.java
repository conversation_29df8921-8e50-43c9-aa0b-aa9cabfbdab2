package com.imile.attendance.report.month.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.util.DateHelper;
import lombok.Data;

import java.util.Date;

/**
 * 考勤周期对象
 * <AUTHOR> chen
 * @Date 2025/6/23 
 * @Description
 */
@Data
public class AttendanceCycleDTO {

    /**
     * 周期起始时间
     */
    private Long attendanceStartCycle;

    /**
     * 周期截止时间
     */
    private Long attendanceEndCycle;


    /**
     * 构建周期对象
     * @param attendanceStartCycle 周期起始时间
     * @param attendanceEndCycle 周期截止时间
     * @return 周期对象
     */
    public static AttendanceCycleDTO of(Long attendanceStartCycle, Long attendanceEndCycle) {
        AttendanceCycleDTO attendanceCycleDTO = new AttendanceCycleDTO();
        attendanceCycleDTO.setAttendanceStartCycle(attendanceStartCycle);
        attendanceCycleDTO.setAttendanceEndCycle(attendanceEndCycle);
        return attendanceCycleDTO;
    }

    /**
     * 格式化dayId
     * @param dayId dayId
     * @return 格式化后的dayId
     */
    public static String formatDayId(Long dayId) {
        return dayId.toString().substring(4, 6) + "-" + dayId.toString().substring(6, 8);
    }

    /**
     * 格式化考勤周期
     * @param startCycleDayId 周期起始时间
     * @param endCycleDayId 周期截止时间
     * @return 格式化后的考勤周期
     */
    public static String formatAttendanceCycle(Long startCycleDayId, Long endCycleDayId) {
        return formatDayId(startCycleDayId) + " ~ " + formatDayId(endCycleDayId);
    }

    /**
     * 将attendanceStartCycle转换为dateStr
     */
    public String attendanceStartCycleDateStr() {
        if (attendanceStartCycle == null) {
            return null;
        }
        return DateHelper.formatYYYYMMDDHHMMSS(DateUtil.parse(attendanceStartCycle.toString(), DatePattern.PURE_DATE_PATTERN));
    }

    public String attendanceEndCycleDateStr() {
        if (attendanceEndCycle == null) {
            return null;
        }
        return DateHelper.formatYYYYMMDDHHMMSS(DateUtil.parse(attendanceEndCycle.toString(), DatePattern.PURE_DATE_PATTERN));
    }




    public static void main(String[] args) {
        AttendanceCycleDTO attendanceCycleDTO = AttendanceCycleDTO.of(20250101L, 20250131L);
        System.out.println(formatAttendanceCycle(attendanceCycleDTO.getAttendanceStartCycle(), attendanceCycleDTO.getAttendanceEndCycle()));
        System.out.println(attendanceCycleDTO.attendanceStartCycleDateStr());
        System.out.println(attendanceCycleDTO.attendanceEndCycleDateStr());
    }
}
