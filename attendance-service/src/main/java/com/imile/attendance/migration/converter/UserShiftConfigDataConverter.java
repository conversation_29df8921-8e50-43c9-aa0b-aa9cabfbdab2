package com.imile.attendance.migration.converter;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceClassEmployeeConfigDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigHistoryDO;
import com.imile.attendance.migration.service.MappingPunchClassConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 排班配置数据转换器
 * 实现HRMS排班数据到新系统排班数据的转换
 *
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Slf4j
@Component
public class UserShiftConfigDataConverter {

    @Resource
    private MappingPunchClassConfigService mappingPunchClassConfigService;

    @Resource
    private DefaultIdWorker defaultIdWorker;

    /**
     * 批量将HRMS排班数据转换为新系统排班数据
     *
     * @param hrmsDataList HRMS排班数据列表
     * @return 新系统排班数据列表
     */
    public List<UserShiftConfigDO> convertFromHrmsBatch(List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataList) {
        if (hrmsDataList == null || hrmsDataList.isEmpty()) {
            log.warn("HRMS排班数据列表为空，跳过批量转换");
            return Collections.emptyList();
        }

        long startTime = System.currentTimeMillis();

        try {
            // 第一步：提取所有需要查询的HR班次ID
            Set<Long> hrPunchClassIds = hrmsDataList.stream()
                    .map(HrmsAttendanceClassEmployeeConfigDO::getClassId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 第二步：批量查询映射关系，构建内存Map
            Map<Long, MappingPunchClassConfigDO> mappingMap = Collections.emptyMap();
            if (!hrPunchClassIds.isEmpty()) {
                List<Long> hrPunchClassIdList = new ArrayList<>(hrPunchClassIds);
                List<MappingPunchClassConfigDO> mappingList = mappingPunchClassConfigService.listByHrPunchClassIds(hrPunchClassIdList);
                mappingMap = mappingList.stream()
                        .collect(Collectors.toMap(
                                MappingPunchClassConfigDO::getHrPunchClassId,
                                Function.identity(),
                                (existing, replacement) -> existing // 处理重复key的情况
                        ));

                log.debug("批量查询班次映射关系完成, 查询HR班次ID数量: {}, 获得映射关系数量: {}",
                        hrPunchClassIds.size(), mappingMap.size());
            }

            // 第三步：批量转换数据
            List<UserShiftConfigDO> resultList = new ArrayList<>();
            for (HrmsAttendanceClassEmployeeConfigDO hrmsData : hrmsDataList) {
                UserShiftConfigDO convertedData = convertFromHrmsWithMapping(hrmsData, mappingMap);
                if (convertedData != null) {
                    resultList.add(convertedData);
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("HRMS排班数据批量转换完成, 输入数量: {}, 输出数量: {}, 耗时: {}ms",
                    hrmsDataList.size(), resultList.size(), endTime - startTime);

            return resultList;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("HRMS排班数据批量转换失败, 输入数量: {}, 耗时: {}ms",
                    hrmsDataList.size(), endTime - startTime, e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量将HRMS排班数据转换为历史排班数据
     *
     * @param hrmsDataList HRMS排班数据列表
     * @return 历史排班数据列表
     */
    public List<UserShiftConfigHistoryDO> convertFromHrmsToHistoryBatch(List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataList) {
        if (hrmsDataList == null || hrmsDataList.isEmpty()) {
            log.warn("HRMS排班数据列表为空，跳过批量历史数据转换");
            return Collections.emptyList();
        }

        long startTime = System.currentTimeMillis();

        try {
            // 先批量转换为新数据格式
            List<UserShiftConfigDO> newDataList = convertFromHrmsBatch(hrmsDataList);
            if (newDataList.isEmpty()) {
                log.warn("批量转换为新数据格式失败，跳过历史数据转换, 输入数量: {}", hrmsDataList.size());
                return Collections.emptyList();
            }

            // 再转换为历史数据格式
            List<UserShiftConfigHistoryDO> historyDataList = new ArrayList<>();
            for (UserShiftConfigDO newData : newDataList) {
                UserShiftConfigHistoryDO historyData = new UserShiftConfigHistoryDO();
                BeanUtils.copyProperties(newData, historyData);

                // 重新设置ID（历史表需要独立的ID）
                historyData.setId(defaultIdWorker.nextId());

                historyDataList.add(historyData);
            }

            long endTime = System.currentTimeMillis();
            log.info("HRMS排班数据批量转换为历史数据完成, 输入数量: {}, 输出数量: {}, 耗时: {}ms",
                    hrmsDataList.size(), historyDataList.size(), endTime - startTime);

            return historyDataList;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("HRMS排班数据批量转换为历史数据失败, 输入数量: {}, 耗时: {}ms",
                    hrmsDataList.size(), endTime - startTime, e);
            return Collections.emptyList();
        }
    }

    /**
     * day_punch_type到day_shift_rule的转换逻辑
     * 将day_punch_type的PH改成H，其他不变
     *
     * @param dayPunchType 原始打卡类型
     * @return 转换后的排班规则
     */
    private String convertDayPunchType(String dayPunchType) {
        if (StringUtils.isBlank(dayPunchType)) {
            return dayPunchType;
        }

        // PH改成H，其他不变
        if (BusinessConstant.ShiftMigration.DAY_PUNCH_TYPE_PH.equals(dayPunchType)) {
            log.debug("day_punch_type转换: {} -> {}", dayPunchType, BusinessConstant.ShiftMigration.DAY_SHIFT_RULE_H);
            return BusinessConstant.ShiftMigration.DAY_SHIFT_RULE_H;
        }

        return dayPunchType;
    }

    /**
     * 将HRMS排班数据转换为新系统排班数据
     *
     * @param hrmsData   HRMS排班数据
     * @param mappingMap 班次映射Map，key为HR班次ID，value为映射关系
     * @return 新系统排班数据
     */
    private UserShiftConfigDO convertFromHrmsWithMapping(HrmsAttendanceClassEmployeeConfigDO hrmsData,
                                                         Map<Long, MappingPunchClassConfigDO> mappingMap) {
        if (hrmsData == null) {
            log.warn("HRMS排班数据为空，跳过转换");
            return null;
        }

        try {
            UserShiftConfigDO newData = new UserShiftConfigDO();

            // 设置新的ID
            newData.setId(defaultIdWorker.nextId());

            // 基础字段映射
            newData.setUserId(hrmsData.getUserId());
            newData.setAttendanceConfigId(hrmsData.getAttendanceConfigId());
            newData.setClassTime(hrmsData.getClassTime());
            newData.setDayId(hrmsData.getDayId());
            newData.setIsLatest(hrmsData.getIsLatest());

            newData.setIsDelete(hrmsData.getIsDelete());
            newData.setRecordVersion(hrmsData.getRecordVersion());
            newData.setCreateDate(hrmsData.getCreateDate());
            newData.setCreateUserCode(hrmsData.getCreateUserCode());
            newData.setCreateUserName(hrmsData.getCreateUserName());
            newData.setLastUpdDate(hrmsData.getLastUpdDate());
            newData.setLastUpdUserCode(hrmsData.getLastUpdUserCode());
            newData.setLastUpdUserName(hrmsData.getLastUpdUserName());

            // day_punch_type -> day_shift_rule 转换（非工作日的转换）
            newData.setDayShiftRule(convertDayPunchType(hrmsData.getDayPunchType()));

            // 映射关系转换：class_id -> punch_class_config_id（使用预构建的Map）
            if (hrmsData.getClassId() != null) {
                MappingPunchClassConfigDO mapping = mappingMap.get(hrmsData.getClassId());
                if (mapping != null) {
                    newData.setPunchClassConfigId(mapping.getPunchClassConfigId());
                    //班次id不为空，说明是具体的班次，使用映射的班次名称（不能使用原始的班次名称）
                    newData.setDayShiftRule(mapping.getPunchClassConfigName());
                    log.debug("班次映射转换成功, hrClassId: {} -> punchClassConfigId: {}",
                            hrmsData.getClassId(), mapping.getPunchClassConfigId());
                } else {
                    log.warn("未找到班次映射关系, hrClassId: {}, userId: {}, dayId: {}",
                            hrmsData.getClassId(), hrmsData.getUserId(), hrmsData.getDayId());
                    // 映射关系不存在时，设置为null，但不影响其他数据的转换
                    newData.setPunchClassConfigId(null);
                }
            }

            //设置班次来源和类型
            newData.setDataSource(hrmsData.getDataSource());
//            ShiftSourceEnum shiftSourceEnum = ShiftSourceEnum.getByCode(hrmsData.getDataSource());
//            if (shiftSourceEnum != null) {
//                newData.setShiftType(shiftSourceEnum.getCategory().getCode());
//            } else {
//                newData.setShiftType(BusinessConstant.ShiftMigration.DEFAULT_SHIFT_TYPE);
//            }
            // 从HRMS迁移过来的数据，统一设置为从HRMS迁移过来的类型
            newData.setShiftType(ShiftTypeEnum.MIGRATE_FROM_HRMS.getCode());
            // 设置任务标识，用于标记迁移数据
            newData.setTaskFlag(BusinessConstant.ShiftMigration.MIGRATE_FROM_HRMS + ":HR_PUNCH_CONFIG_ID:" +
                    hrmsData.getPunchConfigId() + ":HR_CLASS_ID:" + hrmsData.getClassId());

            log.debug("HRMS排班数据转换成功, hrmsId: {} -> newId: {}", hrmsData.getId(), newData.getId());
            return newData;

        } catch (Exception e) {
            log.error("HRMS排班数据转换失败, hrmsId: {}, userId: {}, dayId: {}",
                    hrmsData.getId(), hrmsData.getUserId(), hrmsData.getDayId(), e);
            return null;
        }
    }

    /**
     * 验证转换后的数据是否有效
     *
     * @param convertedData 转换后的数据
     * @return 是否有效
     */
    public boolean validateConvertedData(UserShiftConfigDO convertedData) {
        if (convertedData == null) {
            return false;
        }

        // 检查必填字段
        if (convertedData.getUserId() == null) {
            log.warn("转换后数据缺少用户ID, dataId: {}", convertedData.getId());
            return false;
        }

        if (convertedData.getDayId() == null) {
            log.warn("转换后数据缺少日期ID, dataId: {}, userId: {}",
                    convertedData.getId(), convertedData.getUserId());
            return false;
        }

        return true;
    }

    /**
     * 验证转换后的历史数据是否有效
     *
     * @param convertedData 转换后的历史数据
     * @return 是否有效
     */
    public boolean validateConvertedHistoryData(UserShiftConfigHistoryDO convertedData) {
        if (convertedData == null) {
            return false;
        }

        // 检查必填字段
        if (convertedData.getUserId() == null) {
            log.warn("转换后历史数据缺少用户ID, dataId: {}", convertedData.getId());
            return false;
        }

        if (convertedData.getDayId() == null) {
            log.warn("转换后历史数据缺少日期ID, dataId: {}, userId: {}",
                    convertedData.getId(), convertedData.getUserId());
            return false;
        }

        return true;
    }
}
