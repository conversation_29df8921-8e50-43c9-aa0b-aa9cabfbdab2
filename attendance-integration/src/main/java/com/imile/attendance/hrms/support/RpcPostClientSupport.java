package com.imile.attendance.hrms.support;

import com.imile.attendance.hrms.RpcPostClient;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.api.base.param.PostConditionParam;
import com.imile.hrms.api.base.result.PostDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Component
public class RpcPostClientSupport {

    @Resource
    private RpcPostClient rpcPostClient;

    public List<PostDTO> listByPostList(List<Long> postIds, String status) {
        if (CollectionUtils.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        PostConditionParam postConditionParam = new PostConditionParam();
        postConditionParam.setPostCodeList(postIds.stream()
                .map(String::valueOf)
                .collect(Collectors.toList())
        );
        postConditionParam.setStatus(status);
        return rpcPostClient.listPostByCondition(postConditionParam);
    }
}
