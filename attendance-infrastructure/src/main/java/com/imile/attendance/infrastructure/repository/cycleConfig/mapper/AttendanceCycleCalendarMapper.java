package com.imile.attendance.infrastructure.repository.cycleConfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleCalendarDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 考勤周期日历表 Mapper 接口
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Mapper
@Repository
public interface AttendanceCycleCalendarMapper extends BaseMapper<AttendanceCycleCalendarDO> {

}