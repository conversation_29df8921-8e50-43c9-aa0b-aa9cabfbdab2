package com.imile.attendance.hrms.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.hrms.RpcPostClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hrms.api.base.api.PostApi;
import com.imile.hrms.api.base.param.PostConditionParam;
import com.imile.hrms.api.base.result.PostDTO;
import com.imile.rpc.common.RpcResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Component
public class RpcPostClientImpl implements RpcPostClient {


    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 15000)
    private PostApi postApi;


    /**
     * 这里不用com.imile.hrms.api.base.api.PostApi#getPostByCode(java.lang.String)原因是查不到直接岗位报错影响考勤业务
     *
     * @param id 岗位ID
     * @return
     */
    @Override
//    @Cached(name = Constants.CacheKey.POST_CACHE_KEY,
//            key = "#id",
//            cacheType = CacheType.REMOTE,
//            expire = 10,
//            timeUnit = TimeUnit.MINUTES
//    )
    public PostDTO getPostById(Long id) {
        PostConditionParam param = new PostConditionParam();
        param.setPostCodeList(Collections.singletonList(String.valueOf(id)));
        RpcResult<List<PostDTO>> rpcResult = postApi.listPostByCondition(param);
        List<PostDTO> postDTOList = RpcResultProcessor.process(rpcResult);
        if (CollectionUtils.isEmpty(postDTOList)) {
            return new PostDTO();
        }
        return postDTOList.get(0);
    }

    @Override
    public List<PostDTO> listPostByCondition(PostConditionParam param) {
        return RpcResultProcessor.process(postApi.listPostByCondition(param));
    }
}
