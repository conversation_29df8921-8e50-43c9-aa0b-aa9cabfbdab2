package com.imile.attendance.infrastructure.repository.punch.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.punch.dto.PunchCardRecordDTO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.punch.query.UserPunchCardRecordQuery;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
public interface EmployeePunchRecordDao extends IService<EmployeePunchRecordDO> {

    List<EmployeePunchRecordDO> getPunchList(Long dayId, String userCode, String punchType);

    List<EmployeePunchRecordDO> listRecords(EmployeePunchCardRecordQuery recordQuery);

    /**
     * 根据条件获取打卡记录
     */
    List<EmployeePunchRecordDO> selectListByCondition(Long dayId, String country, List<String> userCodes);

    /**
     * 根据条件获取打卡记录
     */
    List<EmployeePunchRecordDO> selectListByCondition(List<String> countryList, Date startTime, Date endTime);

    /**
     * 获取最近一条绑定手机的打卡记录
     */
    List<EmployeePunchRecordDO> getPunchListByMobileConfigId(List<Long> mobileConfigId);

    List<PunchCardRecordDTO> listRecord(UserPunchCardRecordQuery query);

    /**
     * 根据打卡方式查询时间范围内的用户集合
     */
    List<Long> selectUserIdByPunchCardTypes(Long startDayId, Long endDayId, List<String> punchCardTypeList);

    /**
     * 获取一段时间范围内的用户的打卡方式
     */
    List<String> selectPunchCardTypeByUserCode(String userCode, Long startDayId, Long endDayId);

    /**
     * 根据数据来源sourceType和day_id查询打卡记录
     */
    List<EmployeePunchRecordDO> listBySourceTypeAndDayId(String sourceType, List<String> dayIds);

    void removeByCountry(String country, Long id);

    List<Long> listHrPunchRecord(List<Long> hrPunchRecordList, Long startDayId);

}
