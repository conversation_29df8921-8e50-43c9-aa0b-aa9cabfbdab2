package com.imile.attendance.shift.factory;

import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.calendar.UserCalendarService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.shift.UserShiftLogService;
import com.imile.attendance.shift.command.BatchShiftCommand;
import com.imile.attendance.shift.command.BatchShiftDayCommand;
import com.imile.attendance.shift.command.DaysConfigCommand;
import com.imile.attendance.shift.command.UserShiftConfigAddCommand;
import com.imile.attendance.shift.dto.BatchUserShiftCheckDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18
 * @Description
 */
@Slf4j
@Component
public class CustomShiftConfigFactory extends UserShiftConfigAbstractFactory {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private UserCalendarService userCalendarService;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private UserShiftLogService userShiftLogService;

    @Override
    public boolean isMatch(String shiftType) {
        return ShiftTypeEnum.CUSTOM_SHIFT.getCode().equals(shiftType);
    }

    public void addShift(UserShiftConfigAddCommand addCommand) {
        log.info("Adding user shift config:{}", addCommand);
        if (CollectionUtils.isEmpty(addCommand.getDaysConfigParamList())) {
            return;
        }
        doShift(
                Collections.singletonList(addCommand.getUserId()),
                ShiftSourceEnum.PAGE_SHIFT,
                (taskFlag) -> doAddShift(addCommand, taskFlag)
        );
    }


    private void doAddShift(UserShiftConfigAddCommand addCommand, String taskFlag) {
        Long userId = addCommand.getUserId();
        AttendanceUser attendanceUser = userService.getByUserId(userId);
        checkSchedulingLimit(addCommand.getFromPage(), attendanceUser);

        //查询该用户的考勤日历
        CalendarConfigDO userCalendarConfig = userCalendarService.getUserCalendarConfig(userId);
        if (null == userCalendarConfig) {
            throw BusinessLogicException.getException(ErrorCodeEnum.USER_NOT_HAVE_CALENDAR);
        }

        List<DaysConfigCommand> daysConfigParamList = addCommand.getDaysConfigParamList()
                .stream()
                .filter(item -> StringUtils.isNotBlank(item.getDayShiftRule()))
                .collect(Collectors.toList());
        daysConfigParamList.forEach(item -> {
            if (item.getDate() != null) {
                Date date = item.getDate();
                item.setDayId(DateHelper.getDayId(date));
                item.setYear((long) DateHelper.year(date));
                item.setMonth(DateHelper.formatMonth(date));
                item.setDay((long) DateHelper.dayOfMonth(date));
                item.setYearMonth(item.getYear().toString() + item.getMonth());
            }
        });
        //找出有排班的天数
        List<Long> dayIdList = daysConfigParamList.stream()
                .filter(item -> item.getDayId() != null && StringUtils.isNotBlank(item.getDayShiftRule()))
                .sorted(Comparator.comparing(DaysConfigCommand::getDayId))
                .map(DaysConfigCommand::getDayId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dayIdList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_CLASS_SAVE_NOT_EMPTY);
        }
        List<UserShiftConfigDO> oldUserShiftConfigDOList = new ArrayList<>();
        queryAndSetOldRecord(Collections.singletonList(addCommand.getUserId()), dayIdList, oldUserShiftConfigDOList);

        // 新增数据
        List<UserShiftConfigDO> addUserShiftConfigList = new ArrayList<>();
        for (DaysConfigCommand daysConfigCommand : daysConfigParamList) {
            shiftDayInfoBuild(
                    addCommand.getUserId(),
                    DateHelper.getDayId(daysConfigCommand.getDate()),
                    daysConfigCommand.getClassId(),
                    daysConfigCommand.getDayShiftRule(),
                    userCalendarConfig.getId(),
                    ShiftSourceEnum.PAGE_SHIFT,
                    taskFlag,
                    addUserShiftConfigList
            );
        }
        userShiftConfigManage.batchShiftUpdateOrAdd(oldUserShiftConfigDOList, addUserShiftConfigList);
        logRecordService.recordOperation(
                addUserShiftConfigList.get(0),
                LogRecordOptions.buildWithRemark(OperationTypeEnum.ADD_SHIFT.getCode(),
                        userShiftLogService.addShiftLogRecord(attendanceUser, addUserShiftConfigList)
                )
        );
    }


    public void batchShift(BatchShiftCommand batchShiftCommand) {
        log.info("批量排班:{}", batchShiftCommand);
        List<Long> userIdList = batchShiftCommand.getUserIdList();
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        doShift(
                userIdList,
                ShiftSourceEnum.BATCH_SHIFT,
                (taskFlag) -> {
                    BatchUserShiftCheckDTO batchUserShiftCheckDTO = checkCustomShift(batchShiftCommand.getFromPage(), userIdList);
                    if (batchUserShiftCheckDTO.isAnyNotMatch()) {
                        throw BusinessLogicException.getException(ErrorCodeEnum.BATCH_SHIFT_USER_NOT_MATCH,
                                batchUserShiftCheckDTO.getCurrentUserCount(),
                                batchUserShiftCheckDTO.getNotMatchUserNameStr());
                    }

                    //查询该用户的考勤日历
                    List<CalendarConfigRangeDO> calendarConfigRangeDOList = calendarManage.selectCalendarConfigRange(userIdList);
                    Map<Long, List<CalendarConfigRangeDO>> calendarConfigRangeMap = calendarConfigRangeDOList.stream()
                            .collect(Collectors.groupingBy(CalendarConfigRangeDO::getBizId));

                    //查询用户旧的排班数据和记录
                    List<BatchShiftDayCommand> batchShiftDayCommandList = batchShiftCommand.getBatchShiftDayParamList();
                    List<Long> dayIdList = batchShiftDayCommandList.stream()
                            .map(BatchShiftDayCommand::getDayId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    List<UserShiftConfigDO> oldUserShiftConfigDOList = new ArrayList<>();
                    queryAndSetOldRecord(batchShiftCommand.getUserIdList(), dayIdList, oldUserShiftConfigDOList);

                    List<UserShiftConfigDO> addUserShiftConfigList = new ArrayList<>();

                    for (Long userId : userIdList) {
                        List<CalendarConfigRangeDO> userCalendarConfigRangeList = calendarConfigRangeMap.get(userId);
                        if (CollectionUtils.isEmpty(userCalendarConfigRangeList)) {
                            continue;
                        }
                        for (BatchShiftDayCommand dayCommand : batchShiftDayCommandList) {
                            shiftDayInfoBuild(
                                    userId,
                                    dayCommand.getDayId(),
                                    dayCommand.getClassId(),
                                    dayCommand.getDayShiftRule(),
                                    userCalendarConfigRangeList.get(0).getAttendanceConfigId(),
                                    ShiftSourceEnum.BATCH_SHIFT,
                                    taskFlag,
                                    addUserShiftConfigList
                            );
                        }
                    }
                    userShiftConfigManage.batchShiftUpdateOrAdd(oldUserShiftConfigDOList, addUserShiftConfigList);
                    logRecordService.recordOperation(
                            addUserShiftConfigList.get(0),
                            LogRecordOptions.buildWithRemark(OperationTypeEnum.BATCH_SHIFT.getCode(),
                                    userShiftLogService.batchShiftLogRecord(userService.listUsersByIds(userIdList), batchShiftDayCommandList)
                            )
                    );
                }
        );
    }

    public BatchUserShiftCheckDTO checkCustomShift(Boolean fromPage, List<Long> userIdList) {
        List<AttendanceUser> userList = userService.listUsersByIds(userIdList);
        //判断排班限制
        userList.forEach(user -> checkSchedulingLimit(fromPage, user));
        if (userIdList.size() == 1) {
            return BatchUserShiftCheckDTO.initEmpty();
        }
        //多个用户需要校验班次类型和关联的班次是否一致
        return checkBatchUserShiftConfig(userIdList);
    }


    /**
     * 取消指定班次的用户自定义排班
     * 调用场景:
     * 1、班次停用
     */
 /*   public void cancelCustomShift(CancelCustomShiftCommand cancelCustomShiftCommand) {
        log.info("取消用户自定义排班:{}", cancelCustomShiftCommand);
        List<Long> userIdList = cancelCustomShiftCommand.getUserIdList();
        Long classId = cancelCustomShiftCommand.getClassId();
        Date nowDate = new Date();
        long nowDayId = DateHelper.getDayId(nowDate);
        //删除今日之后（包括今日）所有自定义排班
        List<UserShiftConfigDO> oldUserShiftConfigDOList = queryCustomShiftOldRecord(userIdList, classId, nowDayId);
        userShiftConfigManage.batchShiftUpdateOrAdd(oldUserShiftConfigDOList, null);
    }*/

   /* private List<UserShiftConfigDO> queryCustomShiftOldRecord(List<Long> userIdList,
                                                              Long classId,
                                                              Long startDayId) {
        List<UserShiftConfigDO> result = new ArrayList<>();
        List<UserShiftConfigDO> userShiftConfigDOList =
                userShiftConfigManage.selectRecordByUserIdListAndClassId(userIdList, classId, startDayId, null);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return result;
        }

        List<UserShiftConfigDO> filterUserShiftConfigList = userShiftConfigDOList
                .stream()
                .filter(item -> ShiftTypeEnum.CUSTOM_SHIFT.getCode().equals(item.getShiftType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterUserShiftConfigList)) {
            return result;
        }
        filterUserShiftConfigList.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });
        result.addAll(filterUserShiftConfigList);
        return result;
    }*/
    private void queryAndSetOldRecord(List<Long> userIdList, List<Long> dayIdList, List<UserShiftConfigDO> oldUserClassConfigDOList) {
        List<UserShiftConfigDO> userClassConfigDOList = userShiftConfigManage.selectBatchUserRecord(userIdList, dayIdList);
        userClassConfigDOList.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOUpdate(item);
        });
        oldUserClassConfigDOList.addAll(userClassConfigDOList);
    }
}
