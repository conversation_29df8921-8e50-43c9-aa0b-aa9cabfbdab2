package com.imile.attendance.form;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.common.AttendanceBaseService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.ApplicationDataSourceEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.enums.vacation.LeaveUnitEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.biz.leave.param.LeaveAddParam;
import com.imile.attendance.form.vo.FormInfoExportVO;
import com.imile.attendance.form.biz.outOffice.param.OutOfOfficeAddParam;
import com.imile.attendance.form.biz.reissueCard.UserReissueCardCountService;
import com.imile.attendance.form.biz.reissueCard.param.ReissueCardAddParam;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.form.dto.ApprovalCreateRoleUserDTO;
import com.imile.attendance.form.dto.ApprovalCreateUserDTO;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.dto.ApprovalPreviewErrorUserDTO;
import com.imile.attendance.form.dto.ApprovalPreviewSuccessUserDTO;
import com.imile.attendance.form.dto.ApprovalUserInfoDTO;
import com.imile.attendance.form.dto.ClashApplicationInfoDTO;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.form.dto.UserAuthDTO;
import com.imile.attendance.form.mapstruct.AttendanceFormMapstruct;
import com.imile.attendance.form.param.AttendanceApprovalInfoParam;
import com.imile.attendance.form.param.BaseFormParam;
import com.imile.attendance.form.param.DurationDetailParam;
import com.imile.attendance.form.param.RevokeAddParam;
import com.imile.attendance.form.param.UserAuthParam;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.attendance.form.vo.AttendanceApplicationFromDetailVO;
import com.imile.attendance.form.vo.AttendanceApprovalInfoVO;
import com.imile.attendance.form.vo.DurationDetailVO;
import com.imile.attendance.form.vo.UserAuthVO;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.hermes.dto.DictVO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.DictService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionDeptVO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveStageDetailQuery;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.AttendanceApprovalInfoQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.UserDayConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.punch.EmployeePunchRecordService;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.user.UserLeaveService;
import com.imile.attendance.user.UserService;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.attendance.user.vo.UserLeaveResidualVO;
import com.imile.attendance.user.vo.UserOptionVO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateFormatUtils;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigCarryOverService;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.attendance.vacation.UserLeaveDetailService;
import com.imile.attendance.vacation.UserLeaveStageDetailService;
import com.imile.bpm.enums.ApprovalClientTypeEnum;
import com.imile.bpm.enums.ApprovalDataSourceEnum;
import com.imile.bpm.enums.ApprovalOrgEnum;
import com.imile.bpm.enums.ApprovalRoleEnum;
import com.imile.bpm.enums.ApprovalRoleValueEnum;
import com.imile.bpm.enums.LanguageTypeEnum;
import com.imile.bpm.mq.dto.ApprovalCreateRoleUserApiDTO;
import com.imile.bpm.mq.dto.ApprovalCreateUserApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.bpm.mq.dto.ApprovalUserInfoApiDTO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.imile.attendance.util.BaseDOUtil.fillDOInsert;

/**
 * <AUTHOR> chen
 * @Date 2025/3/20
 * @Description 通用审批表单操作服务
 */
@Slf4j
@Service
public class CommonFormOperationService {

    @Resource
    private AttendanceFormManage formManage;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private CountryService countryService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserService userAssociateService;
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private CompanyLeaveConfigCarryOverService companyLeaveConfigCarryOverService;
    @Resource
    private UserLeaveService userLeaveService;
    @Resource
    private UserLeaveDetailService userLeaveDetailService;
    @Resource
    private UserReissueCardCountService userReissueCardCountService;
    @Resource
    private EmployeePunchRecordService punchRecordService;
    @Resource
    private UserLeaveStageDetailService userLeaveStageDetailService;
    @Resource
    private EmployeeAbnormalAttendanceService employeeAbnormalAttendanceService;
    @Resource
    private AttendanceCycleConfigService attendanceCycleConfigService;
    @Resource
    private AttendanceApprovalManage attendanceApprovalManage;
    @Resource
    private AttendanceFormDao attendanceFormDao;
    @Resource
    private DictService dictService;
    @Resource
    private AttendancePermissionService attendancePermissionService;
    @Resource
    private CommonFormCalcService commonFormCalcService;
    @Resource
    private RpcBpmApprovalClient bpmApprovalClient;
    @Resource
    private MigrationService migrationService;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    @Value("#{${country.main.poc.map:{UAE:'2102255',OMN:'2102255',KSA:'2102474',CHN:'2101947',MEX:'2104959',TUR:'2102354',BRA:'2105554',KWT:'2103221',QAT:'2103221',JOR:'2103221',BHR:'2103221',LBN:'2103221',HQ:'2104453',POL:'2104453',DEU:'2104453',FRA:'2104453',GBR:'2104453',CHL:'2104453',RSA:'2104453',NLD:'2104453',AUS:'2104453',ITA:'2104453'}}}")
    private Map<String, String> countryMainPocMap;

    @Value(value = "${mex.qwb.check.flag}")
    private String mexQwbCheckFlag;

    /**
     * 列表查询
     *
     * @param param
     * @return
     */
    public PaginationResult<AttendanceApprovalInfoVO> list(AttendanceApprovalInfoParam param) {
        AttendanceApprovalInfoQuery query = AttendanceFormMapstruct.INSTANCE.mapListParamToApprovalInfoQuery(param);
        query.setLeaveType(param.getLeaveName());
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "ATTENDANCE")) {
            // 获取部门权限
            UserAuthParam userAuthParam = UserAuthParam.builder().userId(RequestInfoHolder.getUserId()).build();
            UserAuthDTO userAuthDTO = userDeptAuthList(userAuthParam);
            if (!checkDeptAuth(query, userAuthDTO)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
        }
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "CLOVER")) {
            query.setUserIdList(Arrays.asList(RequestInfoHolder.getUserId()));
        }
        if (StringUtils.isNotBlank(param.getFormStatus())) {
            query.setFormStatusList(Arrays.asList(param.getFormStatus()));
        }
        query.setExcludeApplicationDataSource(ApplicationDataSourceEnum.IMPORT.getCode());
        List<String> leaveTypeList = Arrays.asList(FormTypeEnum.LEAVE.getCode(),
                FormTypeEnum.LEAVE_REVOKE.getCode(), FormTypeEnum.REISSUE_CARD.getCode(),
                FormTypeEnum.REISSUE_CARD_REVOKE.getCode(), FormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode());
        List<String> formTypeList = query.getFormTypeList();
        Page<AttendanceFormDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getShowCount() > 0);

        PageInfo<AttendanceFormDO> pageInfo;
        // 请假申请列表---特殊处理
        if (leaveTypeList.containsAll(formTypeList)) {
            pageInfo = page.doSelectPageInfo(() -> attendanceFormDao.selectAttendanceApprovalInfoCustom(query));
        } else {
            pageInfo = page.doSelectPageInfo(() -> attendanceFormDao.selectAttendanceApprovalInfo(query));
        }

        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        List<Long> formIdList = pageInfo.getList().stream().map(item -> item.getId()).collect(Collectors.toList());
        List<AttendanceFormAttrDO> attrDOList = formManage.selectFormAttrByFormIdList(formIdList);
        Map<Long, List<AttendanceFormAttrDO>> fromIdMap = attrDOList.stream().collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));

        List<AttendanceFormRelationDO> relationDOList = formManage.selectRelationByRelationIdList(formIdList).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.APPLICATION_FORM.getCode())).collect(Collectors.toList());
        List<Long> relationFormIdList = relationDOList.stream().map(item -> item.getFormId()).collect(Collectors.toList());
        List<AttendanceFormDO> relationFormList = formManage.selectByIdList(relationFormIdList).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.IN_REVIEW.getCode())
                        || StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.PASS.getCode()))
                .collect(Collectors.toList());

        List<Long> deptIdList = pageInfo.getList().stream().filter(item -> Objects.nonNull(item.getDeptId()))
                .map(item -> item.getDeptId()).collect(Collectors.toList());
        List<AttendanceDept> deptDOList = deptService.selectDeptByIds(deptIdList);
        Map<Long, List<AttendanceDept>> deptIdMap = deptDOList.stream().collect(Collectors.groupingBy(AttendanceDept::getId));

        List<Long> postIdList = pageInfo.getList().stream().filter(item -> Objects.nonNull(item.getPostId()))
                .map(item -> item.getPostId()).collect(Collectors.toList());
        List<AttendancePost> entPostDOList = postService.listByPostList(postIdList);
        Map<Long, List<AttendancePost>> postIdMap = entPostDOList.stream().collect(Collectors.groupingBy(AttendancePost::getId));

        List<AttendanceApprovalInfoVO> approvalInfoVOList = new ArrayList<>();
        for (AttendanceFormDO formDO : pageInfo.getList()) {
            AttendanceApprovalInfoVO infoVO = AttendanceFormMapstruct.INSTANCE.mapFormDOToApprovalInfoVO(formDO);
            approvalInfoVOList.add(infoVO);
            FormStatusEnum statusEnum = FormStatusEnum.getInstance(infoVO.getFormStatus());
            if (statusEnum != null) {
                infoVO.setFormStatusDesc(RequestInfoHolder.isChinese() ? statusEnum.getDesc() : statusEnum.getDescEn());
            }
            List<AttendanceDept> userDeptList = deptIdMap.get(formDO.getDeptId());
            if (CollectionUtils.isNotEmpty(userDeptList)) {
                infoVO.setDeptName(RequestInfoHolder.isChinese() ? userDeptList.get(0).getDeptNameCn() : userDeptList.get(0).getDeptNameEn());
            }
            List<AttendancePost> userPostList = postIdMap.get(formDO.getPostId());
            if (CollectionUtils.isNotEmpty(userPostList)) {
                infoVO.setPostName(RequestInfoHolder.isChinese() ? userPostList.get(0).getPostNameCn() : userPostList.get(0).getPostNameEn());
            }
            infoVO.setApplicationCode(formDO.getApplicationCode());
            infoVO.setApplicationFormId(formDO.getId());
            List<AttendanceFormAttrDO> userAttrDOList = fromIdMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(userAttrDOList)) {
                continue;
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE_REVOKE.getCode())) {
                //假期名称(后续单据leave_type都存储假期名称)
                List<AttendanceFormAttrDO> leaveName = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveName)) {
                    infoVO.setLeaveName(leaveName.get(0).getAttrValue());
                    infoVO.setLeaveNameByLang(leaveName.get(0).getAttrValue());
                    //获取假期类型枚举进行翻译
                    Map<String, DictVO> leaveTypeEnumMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.ATTENDANCE_LEAVE_TYPE);
                    Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));
                    DictVO dictVO = lowerleaveTypeEnumMap.get(leaveName.get(0).getAttrValue().toLowerCase());
                    if (Objects.nonNull(dictVO)) {
                        infoVO.setLeaveNameByLang(dictVO.getDataValue());
                    }
                }

                //假期简称
                List<AttendanceFormAttrDO> leaveShortName = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveShortName.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveShortName)) {
                    infoVO.setLeaveShortName(leaveShortName.get(0).getAttrValue());
                }

                //请假开始时间
                List<AttendanceFormAttrDO> leaveStartDate = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveStartDate)) {
                    infoVO.setLeaveStartDate(DateUtil.parse(leaveStartDate.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }

                //请假结束时间
                List<AttendanceFormAttrDO> leaveEndDate = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveEndDate)) {
                    infoVO.setLeaveEndDate(DateUtil.parse(leaveEndDate.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }
                List<AttendanceFormAttrDO> dayDurationAttr = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dayDurationAttr)) {
                    List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayDurationAttr.get(0).getAttrValue(), DayDurationInfoDTO.class);
                    BigDecimal days = BigDecimal.ZERO;
                    BigDecimal hours = BigDecimal.ZERO;
                    BigDecimal minutes = BigDecimal.ZERO;
                    for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                        days = days.add(dayDurationInfoDTO.getDays());
                        hours = hours.add(dayDurationInfoDTO.getHours());
                        minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                    }
                    if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                        infoVO.setExpectedLeaveTime(days + "days" + hours + "hours" + minutes + "minutes");
                        if (RequestInfoHolder.isChinese()) {
                            infoVO.setExpectedLeaveTime(days + "天" + hours + "小时" + minutes + "分钟");
                        }
                    }
                }

                //请假单位
                List<AttendanceFormAttrDO> leaveUnit = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveUnit.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveUnit)) {
                    infoVO.setLeaveUnit(leaveUnit.get(0).getAttrValue());
                }
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE_REVOKE.getCode())) {
                //外勤开始时间
                List<AttendanceFormAttrDO> outOfOfficeStartDate = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeStartDate)) {
                    infoVO.setOutOfOfficeStartDate(DateUtil.parse(outOfOfficeStartDate.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }

                //外勤结束时间
                List<AttendanceFormAttrDO> outOfOfficeEndDate = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeEndDate)) {
                    infoVO.setOutOfOfficeEndDate(DateUtil.parse(outOfOfficeEndDate.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }

                List<AttendanceFormAttrDO> dayDurationAttr = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dayDurationAttr)) {
                    List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayDurationAttr.get(0).getAttrValue(), DayDurationInfoDTO.class);
                    BigDecimal days = BigDecimal.ZERO;
                    BigDecimal hours = BigDecimal.ZERO;
                    BigDecimal minutes = BigDecimal.ZERO;
                    for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                        days = days.add(dayDurationInfoDTO.getDays());
                        hours = hours.add(dayDurationInfoDTO.getHours());
                        minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                    }
                    if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                        infoVO.setExpectedOutOfOfficeTime(days + "days" + hours + "hours" + minutes + "minutes");
                        if (RequestInfoHolder.isChinese()) {
                            infoVO.setExpectedOutOfOfficeTime(days + "天" + hours + "小时" + minutes + "分钟");
                        }
                    }
                }
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.REISSUE_CARD.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.REISSUE_CARD_REVOKE.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode())) {
                //补卡类型
                List<AttendanceFormAttrDO> reissueCardType = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.reissueCardType.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(reissueCardType)) {
                    infoVO.setReissueCardType(reissueCardType.get(0).getAttrValue());
                }

                //补卡时间
                List<AttendanceFormAttrDO> correctPunchTime = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(correctPunchTime)) {
                    infoVO.setCorrectPunchTime(DateUtil.parse(correctPunchTime.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }
            }

            List<AttendanceFormRelationDO> existRelationList = relationDOList.stream().filter(item -> item.getRelationId().equals(formDO.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existRelationList)) {
                for (AttendanceFormRelationDO relationDO : existRelationList) {
                    List<AttendanceFormDO> existRelationFormList = relationFormList.stream().filter(item -> item.getId().equals(relationDO.getFormId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(existRelationFormList)) {
                        infoVO.setRevokeApplicationCode(existRelationFormList.get(0).getApplicationCode());
                        infoVO.setRevokeApplicationFormId(existRelationFormList.get(0).getId());
                        infoVO.setRevokeFormStatus(existRelationFormList.get(0).getFormStatus());
                        infoVO.setRevokeFormType(existRelationFormList.get(0).getFormType());
                    }
                }
            }
        }
        return PageUtil.getPageResult(approvalInfoVOList, param, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 获取审批单据权限
     *
     * @param param
     * @return
     */
    public List<UserAuthVO> userAuthList(UserAuthParam param) {
        List<UserAuthVO> userAuthVOList = new ArrayList<>();
        AttendanceUser applyUserInfo = userService.getByUserId(param.getUserId());
        if (applyUserInfo == null) {
            return userAuthVOList;
        }
        // 获取用户部门权限
        UserAuthDTO userAuthDTO = userDeptAuthList(param);
        UserDaoQuery condition = AttendanceFormMapstruct.INSTANCE.mapAuthParamToQuery(param);
        condition.setDeptIds(userAuthDTO.getDeptIds());
        List<UserOptionVO> userList = userAssociateService.getUserAssociateList(condition);
        if (CollectionUtils.isEmpty(userList)) {
            return userAuthVOList;
        }
        userAuthVOList = userList.stream()
                .map(s -> UserAuthVO.builder()
                        .userId(s.getId())
                        .userCode(s.getUserCode())
                        .userName(s.getUserName())
                        .deptId(s.getDeptId())
                        .postId(s.getPostId())
                        .build())
                .collect(Collectors.toList());
        return userAuthVOList;
    }

    /**
     * 请假/外勤获取冲突单据和具体时长计算信息
     *
     * @param param
     * @return
     */
    public DurationDetailVO durationDetail(DurationDetailParam param) {
        AttendanceUser userInfo = userService.getByUserId(param.getUserId());
        if (userInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        if (StringUtils.isBlank(param.getCountry())) {
            param.setCountry(userInfo.getLocationCountry());
        }
        DurationDetailVO durationDetailVO = new DurationDetailVO();
        boolean tag = true;
        if (param.getFormId() != null) {
            AttendanceFormDetailBO detailBO = formManage.getFormDetailById(param.getFormId());
            AttendanceFormDO formDO = detailBO.getFormDO();
            if (formDO != null
                    && !StringUtils.equalsIgnoreCase(formDO.getFormStatus(), FormStatusEnum.STAGING.getCode())
                    && !StringUtils.equalsIgnoreCase(formDO.getFormStatus(), FormStatusEnum.REJECT.getCode())) {
                List<AttendanceFormAttrDO> attrDOS = detailBO.getAttrDOList();
                Map<String, AttendanceFormAttrDO> attrMap = attrDOS
                        .stream()
                        .collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
                AttendanceFormAttrDO dayDurationAttr = attrMap.get(ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode());
                if (dayDurationAttr != null) {
                    List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayDurationAttr.getAttrValue(), DayDurationInfoDTO.class);
                    //过滤不占用请假时间的日期
                    dayDurationInfoDTOList = dayDurationInfoDTOList.stream()
                            .filter(item -> item.getDays().compareTo(BigDecimal.ZERO) > 0
                                    || item.getHours().compareTo(BigDecimal.ZERO) > 0
                                    || item.getMinutes().compareTo(BigDecimal.ZERO) > 0)
                            .collect(Collectors.toList());
                    durationDetailVO.setDayDurationInfoDTOList(dayDurationInfoDTOList);
                    tag = false;
                }
            }
        }

        CompanyLeaveConfigDO companyLeaveConfig = null;
        if (tag) {
            //查询该假期配置（外勤不需要）
            if (Objects.nonNull(param.getConfigId())) {
                companyLeaveConfig = companyLeaveConfigService.getById(param.getConfigId());
            }
            //查找冲突单据
            List<ClashApplicationInfoDTO> clashApplicationInfoDTOList = new ArrayList<>();
            selectClashApplication(param.getUserId(), param.getStartDate(), param.getEndDate(), clashApplicationInfoDTOList);
            durationDetailVO.setClashApplicationInfoDTOList(clashApplicationInfoDTOList);
            //每天时长计算明细
            List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
            // 考勤周期校验
            attendanceCycleConfigService.checkDateInUserAttendanceCycle(param.getUserId(), param.getStartDate());
            // 计算明细
            dayDurationInfoHandler(param.getUserId(), param.getStartDate(), param.getEndDate(), companyLeaveConfig, dayDurationInfoDTOList);
            //过滤不占用请假时间的日期
            dayDurationInfoDTOList = dayDurationInfoDTOList.stream()
                    .filter(item -> item.getDays().compareTo(BigDecimal.ZERO) > 0
                            || item.getHours().compareTo(BigDecimal.ZERO) > 0
                            || item.getMinutes().compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());
            durationDetailVO.setDayDurationInfoDTOList(dayDurationInfoDTOList);
        }

        BigDecimal days = BigDecimal.ZERO;
        BigDecimal hours = BigDecimal.ZERO;
        BigDecimal minutes = BigDecimal.ZERO;
        for (DayDurationInfoDTO dayDurationInfoDTO : durationDetailVO.getDayDurationInfoDTOList()) {
            days = days.add(dayDurationInfoDTO.getDays());
            hours = hours.add(dayDurationInfoDTO.getHours());
            minutes = minutes.add(dayDurationInfoDTO.getMinutes());
        }
        if (companyLeaveConfig != null && StringUtils.equalsIgnoreCase(companyLeaveConfig.getLeaveUnit(), LeaveUnitEnum.DAYS.getCode())) {
            durationDetailVO.setExpectedTime(days + "days");
            if (RequestInfoHolder.isChinese()) {
                durationDetailVO.setExpectedTime(days + "天");
            }
        }
        // 请假考虑弹性时长：弹性时长存在半个小时的情况，请假结束时间可能存在17:30的情况，所以这边加上了分钟。
        if (companyLeaveConfig != null && StringUtils.equalsIgnoreCase(companyLeaveConfig.getLeaveUnit(), LeaveUnitEnum.HOURS.getCode())) {
            durationDetailVO.setExpectedTime(days + "days" + hours + "hours" + minutes + "minutes");
            if (RequestInfoHolder.isChinese()) {
                durationDetailVO.setExpectedTime(days + "天" + hours + "小时" + minutes + "分钟");
            }
        }
        if (companyLeaveConfig != null && StringUtils.equalsIgnoreCase(companyLeaveConfig.getLeaveUnit(), LeaveUnitEnum.MINUTES.getCode())) {
            durationDetailVO.setExpectedTime(days + "days" + hours + "hours" + minutes + "minutes");
            if (RequestInfoHolder.isChinese()) {
                durationDetailVO.setExpectedTime(days + "天" + hours + "小时" + minutes + "分钟");
            }
        }
        if (companyLeaveConfig == null) {
            durationDetailVO.setExpectedTime(days + "days" + hours + "hours" + minutes + "minutes");
            if (RequestInfoHolder.isChinese()) {
                durationDetailVO.setExpectedTime(days + "天" + hours + "小时" + minutes + "分钟");
            }
        }
        return durationDetailVO;
    }

    /**
     * 查询单据详情
     *
     * @param formId
     * @return
     */
    public AttendanceApplicationFromDetailVO getFromDetail(Long formId) {
        //查询当前单据是否存在
        AttendanceFormDetailBO detailBO = formManage.getFormDetailById(formId);
        AttendanceFormDO formDO = detailBO.getFormDO();
        if (formDO == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        List<AttendanceFormRelationDO> relationDOS = detailBO.getRelationDOList();
        if (CollectionUtils.isEmpty(relationDOS)) {
            relationDOS = new ArrayList<>();
        }
        AttendanceApplicationFromDetailVO detailVO = AttendanceFormMapstruct.INSTANCE.mapFormDOToApprovalDetailInfoVO(formDO);

        List<AttendanceFormAttrDO> attrDOS = detailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> attrMap = attrDOS
                .stream()
                .collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));

        //去user表查询这个人的最新信息，可能部门岗位都变了
        AttendanceUser userInfo = userService.getByUserId(formDO.getUserId());
        if (userInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        AttendanceDept deptInfo = deptService.getByDeptId(userInfo.getDeptId());
        if (deptInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DEPT_NOT_EXITS.getDesc()));
        }
        AttendancePost postInfo = null;
        // 如果是仓内补卡单据，不需要查询岗位，因为仓内外包没有岗位，仓内自有也不查询了:(mex不存在销卡功能，所以不存在其他单据类型)
        if (ObjectUtil.notEqual(detailVO.getFormType(), FormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode())) {
            postInfo = postService.getByPostId(userInfo.getPostId());
            if (postInfo == null) {
                throw BusinessException.get(ErrorCodeEnum.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.POST_NOT_EXITS.getDesc()));
            }
        }
        detailVO.setUserId(userInfo.getId());
        detailVO.setUserCode(userInfo.getUserCode());
        detailVO.setUserName(userInfo.getUserName());
        detailVO.setDeptId(userInfo.getDeptId());
        detailVO.setDeptName(RequestInfoHolder.isChinese() ? deptInfo.getDeptNameCn() : deptInfo.getDeptNameEn());
        detailVO.setPostId(userInfo.getPostId());
        if (ObjectUtil.isNotNull(postInfo)) {
            detailVO.setPostName(RequestInfoHolder.isChinese() ? postInfo.getPostNameCn() : postInfo.getPostNameEn());
        }
        detailVO.setCountry(userInfo.getLocationCountry());
        detailVO.setApplicationCode(formDO.getApplicationCode());
        detailVO.setApplicationFormId(formDO.getId());
        detailVO.setFormType(formDO.getFormType());
        detailVO.setFormStatus(formDO.getFormStatus());
        AttendanceFormAttrDO configId = attrMap.get(ApplicationFormAttrKeyEnum.configID.getLowerCode());
        if (configId != null) {
            detailVO.setConfigId(Long.valueOf(configId.getAttrValue()));
        }
        // 由于单据历史数据问题，请假单leave_type字段暂时不修改，后续leave_type字段都存储假期名称
        AttendanceFormAttrDO leaveName = attrMap.get(ApplicationFormAttrKeyEnum.leaveType.getLowerCode());
        if (leaveName != null) {
            detailVO.setLeaveName(leaveName.getAttrValue());
            detailVO.setLeaveNameByLang(leaveName.getAttrValue());
            //获取假期类型枚举进行翻译
            Map<String, DictVO> leaveTypeEnumMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.ATTENDANCE_LEAVE_TYPE);
            Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));
            DictVO dictVO = lowerleaveTypeEnumMap.get(leaveName.getAttrValue().toLowerCase());
            if (Objects.nonNull(dictVO)) {
                detailVO.setLeaveNameByLang(dictVO.getDataValue());
            }
        }
        AttendanceFormAttrDO leaveShortName = attrMap.get(ApplicationFormAttrKeyEnum.leaveShortName.getLowerCode());
        if (leaveShortName != null) {
            detailVO.setLeaveShortName(leaveShortName.getAttrValue());
        }
        AttendanceFormAttrDO leaveResidueMinutes = attrMap.get(ApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode());
        if (leaveResidueMinutes != null) {
            detailVO.setLeaveResidueMinutes(new BigDecimal(leaveResidueMinutes.getAttrValue()));
        }
        AttendanceFormAttrDO leaveUnitName = attrMap.get(ApplicationFormAttrKeyEnum.leaveUnit.getLowerCode());
        if (leaveUnitName != null) {
            detailVO.setLeaveUnit(leaveUnitName.getAttrValue());
        }


        AttendanceFormAttrDO leaveStartDate = attrMap.get(ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
        if (leaveStartDate != null) {
            detailVO.setLeaveStartDate(DateUtil.parse(leaveStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        AttendanceFormAttrDO leaveEndDate = attrMap.get(ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode());
        if (leaveEndDate != null) {
            detailVO.setLeaveEndDate(DateUtil.parse(leaveEndDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }


        AttendanceFormAttrDO remark = attrMap.get(ApplicationFormAttrKeyEnum.remark.getLowerCode());
        if (remark != null) {
            detailVO.setRemark(remark.getAttrValue());
        }
        AttendanceFormAttrDO attachmentList = attrMap.get(ApplicationFormAttrKeyEnum.attachmentList.getLowerCode());
        if (attachmentList != null) {
            List<AttachmentDTO> attachmentDTOS = JSONObject.parseArray(attachmentList.getAttrValue(), AttachmentDTO.class);
            detailVO.setAttachmentList(attachmentDTOS);
        }

        AttendanceFormAttrDO dayDurationAttr = attrMap.get(ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode());
        if (dayDurationAttr != null) {
            List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayDurationAttr.getAttrValue(), DayDurationInfoDTO.class);
            detailVO.setDayDurationInfoDTOList(dayDurationInfoDTOList);
            BigDecimal days = BigDecimal.ZERO;
            BigDecimal hours = BigDecimal.ZERO;
            BigDecimal minutes = BigDecimal.ZERO;
            for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                days = days.add(dayDurationInfoDTO.getDays());
                hours = hours.add(dayDurationInfoDTO.getHours());
                minutes = minutes.add(dayDurationInfoDTO.getMinutes());
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                if (StringUtils.equalsIgnoreCase(detailVO.getLeaveUnit(), LeaveUnitEnum.DAYS.getCode())) {
                    detailVO.setExpectedLeaveTime(days + "days");
                    if (RequestInfoHolder.isChinese()) {
                        detailVO.setExpectedLeaveTime(days + "天");
                    }
                }
                if (StringUtils.equalsIgnoreCase(detailVO.getLeaveUnit(), LeaveUnitEnum.HOURS.getCode())) {
                    detailVO.setExpectedLeaveTime(days + "days" + hours + "hours");
                    if (RequestInfoHolder.isChinese()) {
                        detailVO.setExpectedLeaveTime(days + "天" + hours + "小时");
                    }
                }
                if (StringUtils.equalsIgnoreCase(detailVO.getLeaveUnit(), LeaveUnitEnum.MINUTES.getCode())) {
                    detailVO.setExpectedLeaveTime(days + "days" + hours + "hours" + minutes + "minutes");
                    if (RequestInfoHolder.isChinese()) {
                        detailVO.setExpectedLeaveTime(days + "天" + hours + "小时" + minutes + "分钟");
                    }
                }
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                detailVO.setExpectedLeaveTime(days + "days" + hours + "hours");
                if (RequestInfoHolder.isChinese()) {
                    detailVO.setExpectedLeaveTime(days + "天" + hours + "小时");
                }
            }
        }

        AttendanceFormAttrDO outOfOfficeStartDate = attrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
        if (outOfOfficeStartDate != null) {
            detailVO.setOutOfOfficeStartDate(DateUtil.parse(outOfOfficeStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        AttendanceFormAttrDO outOfOfficeEndDate = attrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode());
        if (outOfOfficeEndDate != null) {
            detailVO.setOutOfOfficeEndDate(DateUtil.parse(outOfOfficeEndDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }

        //补卡相关信息
        AttendanceFormAttrDO reissueCardDayId = attrMap.get(ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode());
        if (reissueCardDayId != null) {
            detailVO.setReissueCardDayId(Long.valueOf(reissueCardDayId.getAttrValue()));
        }
        AttendanceFormAttrDO reissueCardType = attrMap.get(ApplicationFormAttrKeyEnum.reissueCardType.getLowerCode());
        if (reissueCardType != null) {
            detailVO.setReissueCardType(reissueCardType.getAttrValue());
        }
        AttendanceFormAttrDO residueReissueCardCount = attrMap.get(ApplicationFormAttrKeyEnum.residueReissueCardCount.getLowerCode());
        if (residueReissueCardCount != null) {
            detailVO.setResidueReissueCardCount(Integer.valueOf(residueReissueCardCount.getAttrValue()));
        }
        AttendanceFormAttrDO attendanceStartDate = attrMap.get(ApplicationFormAttrKeyEnum.attendanceStartDate.getLowerCode());
        if (attendanceStartDate != null) {
            detailVO.setAttendanceStartDate(DateUtil.parse(attendanceStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        AttendanceFormAttrDO attendanceEndDate = attrMap.get(ApplicationFormAttrKeyEnum.attendanceEndDate.getLowerCode());
        if (attendanceEndDate != null) {
            detailVO.setAttendanceEndDate(DateUtil.parse(attendanceEndDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        AttendanceFormAttrDO punchConfigClassItemInfo = attrMap.get(ApplicationFormAttrKeyEnum.punchConfigClassItemInfo.getLowerCode());
        if (punchConfigClassItemInfo != null) {
            detailVO.setPunchConfigClassItemInfo(punchConfigClassItemInfo.getAttrValue());
        }
        AttendanceFormAttrDO actualPunchTime = attrMap.get(ApplicationFormAttrKeyEnum.actualPunchTime.getLowerCode());
        if (actualPunchTime != null) {
            detailVO.setActualPunchTime(DateUtil.parse(actualPunchTime.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        AttendanceFormAttrDO correctPunchTime = attrMap.get(ApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode());
        if (correctPunchTime != null) {
            detailVO.setCorrectPunchTime(DateUtil.parse(correctPunchTime.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        List<Long> abnormalIdList = relationDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode())).map(item -> item.getRelationId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(abnormalIdList)) {
            detailVO.setAbnormalId(abnormalIdList.get(0));
        }

        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())
                || StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE_REVOKE.getCode())) {
            if (StringUtils.equalsIgnoreCase(formDO.getFormStatus(), FormStatusEnum.STAGING.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormStatus(), FormStatusEnum.REJECT.getCode())) {
                //获取最新的假期相关信息(暂时适配没有configId的历史数据情况)
                if (StringUtils.isNotBlank(detailVO.getLeaveType())) {
                    List<UserLeaveResidualVO> leaveResidualVOList = userLeaveService.selectUserLeaveResidual(detailVO.getUserId()).stream()
                            .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveType(), detailVO.getLeaveType())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(leaveResidualVOList)) {
                        throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE.getDesc()));
                    }
                    detailVO.setConsumeLeaveType(leaveResidualVOList.get(0).getConsumeLeaveType());
                    detailVO.setIsUploadAttachment(leaveResidualVOList.get(0).getIsUploadAttachment());
                    detailVO.setLeaveUnit(leaveResidualVOList.get(0).getLeaveUnit());
                    detailVO.setLeaveShortName(leaveResidualVOList.get(0).getLeaveShortName());
                    detailVO.setLeaveResidueMinutes(leaveResidualVOList.get(0).getLeaveResidueMinutes());
                }
                //获取最新的假期相关信息
                if (Objects.nonNull(detailVO.getConfigId())) {
                    List<UserLeaveResidualVO> leaveResidualVOList = userLeaveService.selectUserLeaveResidual(detailVO.getUserId()).stream()
                            .filter(item -> Objects.equals(item.getConfigId(), detailVO.getConfigId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(leaveResidualVOList)) {
                        throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE.getDesc()));
                    }
                    detailVO.setConsumeLeaveType(leaveResidualVOList.get(0).getConsumeLeaveType());
                    detailVO.setIsUploadAttachment(leaveResidualVOList.get(0).getIsUploadAttachment());
                    detailVO.setLeaveUnit(leaveResidualVOList.get(0).getLeaveUnit());
                    detailVO.setLeaveShortName(leaveResidualVOList.get(0).getLeaveShortName());
                    detailVO.setLeaveResidueMinutes(leaveResidualVOList.get(0).getLeaveResidueMinutes());
                }
                //暂存的时候，可能连请假时间都没填写，那就无需获取最新的
                if (detailVO.getLeaveStartDate() == null || detailVO.getLeaveEndDate() == null) {
                    return detailVO;
                }
            }
        }

        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())
                || StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE_REVOKE.getCode())) {
            if (StringUtils.equalsIgnoreCase(formDO.getFormStatus(), FormStatusEnum.STAGING.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormStatus(), FormStatusEnum.REJECT.getCode())) {
                //暂存的时候，可能连请假时间都没填写，那就无需获取最新的
                if (detailVO.getOutOfOfficeStartDate() == null || detailVO.getOutOfOfficeEndDate() == null) {
                    return detailVO;
                }
            }
        }

        return detailVO;
    }

    /**
     * 取消单据
     *
     * @param formId
     */
    public void cancel(Long formId) {
        //查询当前单据是否存在
        AttendanceFormDetailBO detailBO = formManage.getFormDetailById(formId);
        AttendanceFormDO formDO = detailBO.getFormDO();
        if (formDO == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //非这些状态的单据不处理
        if (!StringUtils.equalsIgnoreCase(FormStatusEnum.IN_REVIEW.getCode(), formDO.getFormStatus())
                && !StringUtils.equalsIgnoreCase(FormStatusEnum.REJECT.getCode(), formDO.getFormStatus())) {
            throw BusinessException.get(ErrorCodeEnum.FORM_STATUS_NOT_CANCEL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.FORM_STATUS_NOT_CANCEL.getDesc()));
        }
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(formDO.getUserId())) {
            log.info("attendanceMqHandler | userInfo is on old attendance, userCode:{}" + formDO.getUserCode());
            return;
        }
        //查询该审批单有咩有关联异常考勤，如果有，异常考勤状态变为待审批
        List<Long> abnormalIdList = formManage.selectRelationByFormIdList(Arrays.asList(formId)).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode())).map(item -> item.getRelationId()).collect(Collectors.toList());
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        UserCycleReissueCardCountDO userCardConfigDO = null;
        if (CollectionUtils.isNotEmpty(abnormalIdList)) {
            List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectByIdList(abnormalIdList);
            if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
                throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
            }
            abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
            if (!StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
                throw BusinessException.get(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getDesc()));
            }
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);

        }
        userCardConfigDO = this.reissueCardCountHandler(detailBO);

        /*
            取消：需要判断当前审批单状态
                驳回状态的取消：不需要返假期（因为驳回的时候已经返回假期了）
                非驳回状态的（也就是只有审核中状态的，上面有校验，只有审核中、驳回才能走取消逻辑）取消：需要返假期
             注意：只有请假申请单 + 非驳回状态的，取消的才会【返还假期余额、扣减已使用假期余额】。
             销假这种场景，只有审核通过的时候才会【返还假期余额、扣减已使用假期余额】
        */
        List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = Lists.newArrayList();
        // 处理用户请假记录
        UserLeaveRecordDO userLeaveRecord = null;
        if (ObjectUtil.equal(formDO.getFormType(), FormTypeEnum.LEAVE.getCode()) &&
                ObjectUtil.notEqual(formDO.getFormStatus(), FormStatusEnum.REJECT.getCode())) {

            userLeaveRecord = handlerUserLeaveStageDetailList(detailBO, userLeaveStageDetailInfoList, "【attendance或clover审核中】取消请假申请单");
        }
        bpmApprovalClient.backApply(detailBO.getFormDO().getApprovalId());
        formDO.setFormStatus(FormStatusEnum.CANCEL.getCode());
        BaseDOUtil.fillDOUpdate(formDO);
        attendanceApprovalManage.cancel(formDO, abnormalAttendanceDO, userCardConfigDO, userLeaveStageDetailInfoList, userLeaveRecord);
    }

    /**
     * 删除（不包含加班）
     */
    public void delete(Long formId) {
        //查询当前单据是否存在
        AttendanceFormDetailBO formDetailBO = formManage.getFormDetailById(formId);
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        if (formDO == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //只有暂存状态的单据可以被删除
        if (!StringUtils.equalsIgnoreCase(FormStatusEnum.STAGING.getCode(), formDO.getFormStatus())) {
            throw BusinessException.get(ErrorCodeEnum.ONLY_STAGE_STATUS_CAN_BE_DELETE.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ONLY_STAGE_STATUS_CAN_BE_DELETE.getDesc()));
        }
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(formDO.getUserId())) {
            log.info("attendanceMqHandler | userInfo is on old attendance, userCode:{}" + formDO.getUserCode());
            return;
        }
        formDO.setIsDelete(BusinessConstant.Y);
        BaseDOUtil.fillDOUpdate(formDO);
        List<AttendanceFormRelationDO> relationDOS = formDetailBO.getRelationDOList();
        relationDOS.forEach(item -> {
            item.setIsDelete(BusinessConstant.Y);
            BaseDOUtil.fillDOUpdate(item);
        });
        List<AttendanceFormAttrDO> attrDOS = formDetailBO.getAttrDOList();
        attrDOS.forEach(item -> {
            item.setIsDelete(BusinessConstant.Y);
            BaseDOUtil.fillDOUpdate(item);
        });
        formManage.delete(formDO, relationDOS, attrDOS);
    }

    /**
     * 单据信息导出
     *
     * @param param
     * @return
     */
    public PaginationResult<FormInfoExportVO> listExport(AttendanceApprovalInfoParam param) {
        if (StringUtils.isNotBlank(param.getDeptIdString())) {
            List<String> deptIdStringList = Arrays.asList(param.getDeptIdString().split(","));
            List<Long> deptIdList = new ArrayList<>();
            deptIdStringList.forEach(item -> {
                deptIdList.add(Long.valueOf(item));
            });
            param.setDeptIds(deptIdList);
        }
        AttendanceApprovalInfoQuery query = AttendanceFormMapstruct.INSTANCE.mapInitInfoToApprovalApiQuery(param);
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "ATTENDANCE")) {
            // 获取部门权限
            UserAuthParam userAuthParam = UserAuthParam.builder().userId(RequestInfoHolder.getUserId()).build();
            UserAuthDTO userAuthDTO = this.userDeptAuthList(userAuthParam);
            if (!this.checkDeptAuth(query, userAuthDTO)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
        }
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "CLOVER")) {
            query.setUserIdList(Arrays.asList(RequestInfoHolder.getUserId()));
        }
        if (StringUtils.isNotBlank(param.getFormStatus())) {
            query.setFormStatusList(Arrays.asList(param.getFormStatus()));
        }
        query.setExcludeApplicationDataSource(ApplicationDataSourceEnum.IMPORT.getCode());
        Page<AttendanceFormDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getShowCount() > 0);
        PageInfo<AttendanceFormDO> pageInfo = page.doSelectPageInfo(() -> formManage.selectAttendanceApprovalInfo(query));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> formIdList = pageInfo.getList().stream().map(item -> item.getId()).collect(Collectors.toList());
        List<AttendanceFormAttrDO> attrDOList = formManage.selectFormAttrByFormIdList(formIdList);
        List<Long> uesrIdList = pageInfo.getList().stream().map(item -> item.getUserId()).collect(Collectors.toList());
        List<Long> applyUesrIdList = pageInfo.getList().stream().map(item -> item.getApplyUserId()).collect(Collectors.toList());
        List<Long> allUesrIdList = new ArrayList<>();
        allUesrIdList.addAll(uesrIdList);
        allUesrIdList.addAll(applyUesrIdList);
        List<AttendanceUser> userInfoList = userService.listUsersByIds(allUesrIdList);
        List<Long> deptIdList = userInfoList.stream().filter(item -> item.getDeptId() != null).map(item -> item.getDeptId()).collect(Collectors.toList());
        List<Long> postIdList = userInfoList.stream().filter(item -> item.getPostId() != null).map(item -> item.getPostId()).collect(Collectors.toList());
        List<AttendanceDept> deptList = deptService.selectDeptByIds(deptIdList);
        List<AttendancePost> postList = postService.listByPostList(postIdList);
        List<FormInfoExportVO> exportVOList = new ArrayList<>();
        for (AttendanceFormDO formDO : pageInfo.getList()) {
            FormInfoExportVO exportVO = new FormInfoExportVO();
            exportVO.setApplicationCode(formDO.getApplicationCode());
            FormTypeEnum formTypeEnum = FormTypeEnum.getInstance(formDO.getFormType());
            if (Objects.nonNull(formTypeEnum)) {
                exportVO.setFormType(RequestInfoHolder.isChinese()
                        ? formTypeEnum.getDesc() : formTypeEnum.getDescEn());
            }
            FormStatusEnum formStatusEnum = FormStatusEnum.getInstance(formDO.getFormStatus());
            if (Objects.nonNull(formStatusEnum)) {
                exportVO.setFormStatus(RequestInfoHolder.isChinese()
                        ? formStatusEnum.getDesc() : formStatusEnum.getDescEn());
            }
            exportVO.setCreateDate(formDO.getCreateDate());
            List<AttendanceFormAttrDO> leaveNameDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(leaveNameDOList)) {
                exportVO.setLeaveName(leaveNameDOList.get(0).getAttrValue());
            }
            String leaveName = param.getLeaveName();
            if (StringUtils.isNotBlank(leaveName) && !leaveName.equals(exportVO.getLeaveName())) {
                continue;
            }
            if (query.getFormTypeList().contains(FormTypeEnum.LEAVE.getCode())) {
                List<AttendanceFormAttrDO> leaveStartDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveStartDateDOList)) {
                    exportVO.setStartDate(leaveStartDateDOList.get(0).getAttrValue());
                }
                List<AttendanceFormAttrDO> leaveEndDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveEndDateDOList)) {
                    exportVO.setEndDate(leaveEndDateDOList.get(0).getAttrValue());
                }
            }
            if (query.getFormTypeList().contains(FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                List<AttendanceFormAttrDO> outOfOfficeStartDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDOList)) {
                    exportVO.setStartDate(outOfOfficeStartDateDOList.get(0).getAttrValue());
                }
                List<AttendanceFormAttrDO> outOfOfficeEndDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeEndDateDOList)) {
                    exportVO.setEndDate(outOfOfficeEndDateDOList.get(0).getAttrValue());
                }
            }
            if (query.getFormTypeList().contains(FormTypeEnum.REISSUE_CARD.getCode())) {
                List<AttendanceFormAttrDO> reissueCardTypeList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.reissueCardType.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(reissueCardTypeList)) {
                    String reissueCardType = reissueCardTypeList.get(0).getAttrValue();
                    AttendanceAbnormalTypeEnum reissueCardEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(reissueCardType);
                    exportVO.setReissueType(Objects.isNull(reissueCardEnum)
                            ? reissueCardType : RequestInfoHolder.isChinese()
                            ? reissueCardEnum.getDesc() : reissueCardEnum.getDescEn());
                }
                List<AttendanceFormAttrDO> reissueCardDateList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(reissueCardDateList)) {
                    exportVO.setReissueDate(reissueCardDateList.get(0).getAttrValue());
                }
            }
            List<AttendanceFormAttrDO> dayInfoList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dayInfoList)) {
                List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoList.get(0).getAttrValue(), DayDurationInfoDTO.class);

                BigDecimal days = BigDecimal.ZERO;
                BigDecimal hours = BigDecimal.ZERO;
                BigDecimal minutes = BigDecimal.ZERO;

                for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                    days = days.add(dayDurationInfoDTO.getDays());
                    hours = hours.add(dayDurationInfoDTO.getHours());
                    minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                }
                String descCN = days + "天" + hours + "小时" + minutes + "分钟";
                String descEN = days + "days" + hours + "hours" + minutes + "minutes";
                Map<String, String> expectedLeaveTimeMap = new HashMap<>();
                expectedLeaveTimeMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
                expectedLeaveTimeMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
                exportVO.setLeaveHours(descEN);
            }

            exportVOList.add(exportVO);
            List<AttendanceUser> existUserInfoList = userInfoList
                    .stream()
                    .filter(item -> item.getId().equals(formDO.getUserId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existUserInfoList)) {
                continue;
            }
            exportVO.setUserCode(existUserInfoList.get(0).getUserCode());
            exportVO.setUserName(existUserInfoList.get(0).getUserName());
            exportVO.setCountry(existUserInfoList.get(0).getLocationCountry());
            List<AttendanceDept> existDeptDOList = deptList
                    .stream()
                    .filter(item -> item.getId().equals(existUserInfoList.get(0).getDeptId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existDeptDOList)) {
                exportVO.setDeptName(existDeptDOList.get(0).getDeptNameEn());
            }
            List<AttendancePost> existPostDOList = postList
                    .stream()
                    .filter(item -> item.getId().equals(existUserInfoList.get(0).getPostId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existPostDOList)) {
                exportVO.setPostName(existPostDOList.get(0).getPostNameEn());
            }

            List<AttendanceUser> existApplyUserInfoDOList = userInfoList.stream().filter(item -> item.getId().equals(formDO.getApplyUserId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existApplyUserInfoDOList)) {
                continue;
            }
            exportVO.setApplyUserCode(existApplyUserInfoDOList.get(0).getUserCode());
            exportVO.setApplyUserName(existApplyUserInfoDOList.get(0).getUserName());
        }
        return PageUtil.getPageResult(exportVOList, param, (int) pageInfo.getTotal(), pageInfo.getPages());
    }


    /**
     * 撤销单据校验
     *
     * @param userId
     * @param formId
     * @param leaveStartDate
     */
    public void revokeCheck(Long userId,
                            Long formId,
                            AttendanceFormAttrDO leaveStartDate) {
        // 撤销单据重复性校验
        this.repeatRevokeCheck(formId);
        // 请假开始时间校验
        if (leaveStartDate == null) {
            throw BusinessException.get(ErrorCodeEnum.LEAVE_START_DATE_NOT_EMPTY.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.LEAVE_START_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(leaveStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        // 考勤周期校验
        attendanceCycleConfigService.checkDateInUserAttendanceCycle(userId, startDate);
    }

    /**
     * 撤销单据重复性校验
     *
     * @param formId
     */
    public void repeatRevokeCheck(Long formId) {
        List<Long> revokeFormIdList = formManage.selectRelationByRelationIdList(Collections.singletonList(formId))
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.APPLICATION_FORM.getCode()))
                .map(AttendanceFormRelationDO::getFormId)
                .collect(Collectors.toList());
        List<AttendanceFormDO> revokeFormList = formManage.selectByIdList(revokeFormIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.IN_REVIEW.getCode()) ||
                        StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.PASS.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(revokeFormList)) {
            throw BusinessException.get(ErrorCodeEnum.NOT_REVOKE_REPEAT.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.NOT_REVOKE_REPEAT.getDesc()));
        }
    }

    /**
     * 查找冲突单据
     */
    public void selectClashApplication(Long userId, Date startDate, Date endDate,
                                       List<ClashApplicationInfoDTO> clashApplicationInfoDTOList) {
        ApplicationFormQuery formQuery = new ApplicationFormQuery();
        formQuery.setUserId(userId);
        formQuery.setFromTypeList(Arrays.asList(FormTypeEnum.LEAVE.getCode(), FormTypeEnum.OUT_OF_OFFICE.getCode()));
        formQuery.setStatusList(Arrays.asList(FormStatusEnum.IN_REVIEW.getCode(), FormStatusEnum.PASS.getCode()));
        List<AttendanceFormDO> formDOList = formManage.selectForm(formQuery);
        List<Long> formIdList = formDOList.stream().map(AttendanceFormDO::getId).collect(Collectors.toList());
        //查找属性表
        List<AttendanceFormAttrDO> formAttrDOList = formManage.selectFormAttrByFormIdList(formIdList);
        Map<Long, List<AttendanceFormAttrDO>> formIdMap = formAttrDOList.stream()
                .collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));
        for (AttendanceFormDO formDO : formDOList) {
            List<AttendanceFormAttrDO> existFormList = formIdMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(existFormList)) {
                continue;
            }
            Map<String, AttendanceFormAttrDO> attrMap = existFormList.stream()
                    .collect(Collectors.toMap(AttendanceFormAttrDO::getAttrKey, o -> o, (v1, v2) -> v1));
            AttendanceFormAttrDO dayInfoDO = attrMap.get(ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode());
            String descCN = null;
            String descEN = null;
            if (dayInfoDO != null) {
                List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoDO.getAttrValue(), DayDurationInfoDTO.class);
                BigDecimal days = BigDecimal.ZERO;
                BigDecimal hours = BigDecimal.ZERO;
                BigDecimal minutes = BigDecimal.ZERO;
                for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                    days = days.add(dayDurationInfoDTO.getDays());
                    hours = hours.add(dayDurationInfoDTO.getHours());
                    minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                }
                descCN = days + "天" + hours + "小时" + minutes + "分钟";
                descEN = days + "days" + hours + "hours" + minutes + "minutes";
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                AttendanceFormAttrDO configIdDO = attrMap.get(ApplicationFormAttrKeyEnum.configID.getLowerCode());
                AttendanceFormAttrDO leaveNameDO = attrMap.get(ApplicationFormAttrKeyEnum.leaveType.getLowerCode());
                AttendanceFormAttrDO leaveUnitDO = attrMap.get(ApplicationFormAttrKeyEnum.leaveUnit.getLowerCode());
                AttendanceFormAttrDO leaveStartDateDO = attrMap.get(ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
                AttendanceFormAttrDO leaveEndDateDO = attrMap.get(ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode());

                if (leaveStartDateDO == null || leaveEndDateDO == null || leaveNameDO == null || leaveUnitDO == null) {
                    continue;
                }
                Date leaveStartDate = DateFormatUtils.parseDateTime(leaveStartDateDO.getAttrValue());
                Date leaveEndDate = DateFormatUtils.parseDateTime(leaveEndDateDO.getAttrValue());
                if (startDate.compareTo(leaveEndDate) > -1 || endDate.compareTo(leaveStartDate) < 1) {
                    //没有交集，没有冲突
                    continue;
                }
                ClashApplicationInfoDTO clashApplicationInfoDTO = new ClashApplicationInfoDTO();
                clashApplicationInfoDTO.setApplicationCode(formDO.getApplicationCode());
                clashApplicationInfoDTO.setApplicationFormId(formDO.getId());
                clashApplicationInfoDTO.setApprovalId(formDO.getApprovalId());
                clashApplicationInfoDTO.setFormType(formDO.getFormType());
                clashApplicationInfoDTO.setFormStatus(formDO.getFormStatus());
                clashApplicationInfoDTO.setConfigId(Objects.isNull(configIdDO) ? null : Long.parseLong(configIdDO.getAttrValue()));
                clashApplicationInfoDTO.setLeaveName(leaveNameDO.getAttrValue());
                clashApplicationInfoDTO.setLeaveUnit(leaveUnitDO.getAttrValue());
                clashApplicationInfoDTO.setStartDate(leaveStartDate);
                clashApplicationInfoDTO.setEndDate(leaveEndDate);
                clashApplicationInfoDTO.setExpectedTime(RequestInfoHolder.isChinese() ? descCN : descEN);
                clashApplicationInfoDTOList.add(clashApplicationInfoDTO);
                continue;
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                AttendanceFormAttrDO outOfOfficeStartDateDO = attrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
                AttendanceFormAttrDO outOfOfficeEndDateDO = attrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode());
                if (outOfOfficeStartDateDO == null || outOfOfficeEndDateDO == null) {
                    continue;
                }
                Date outOfOfficeStartDate = DateFormatUtils.parseDateTime(outOfOfficeStartDateDO.getAttrValue());
                Date outOfOfficeEndDate = DateFormatUtils.parseDateTime(outOfOfficeEndDateDO.getAttrValue());
                if (startDate.compareTo(outOfOfficeEndDate) > -1 || endDate.compareTo(outOfOfficeStartDate) < 1) {
                    //没有交集，没有冲突
                    continue;
                }
                ClashApplicationInfoDTO clashApplicationInfoDTO = new ClashApplicationInfoDTO();
                clashApplicationInfoDTO.setApplicationCode(formDO.getApplicationCode());
                clashApplicationInfoDTO.setApplicationFormId(formDO.getId());
                clashApplicationInfoDTO.setApprovalId(formDO.getApprovalId());
                clashApplicationInfoDTO.setFormType(formDO.getFormType());
                clashApplicationInfoDTO.setFormStatus(formDO.getFormStatus());
                clashApplicationInfoDTO.setStartDate(outOfOfficeStartDate);
                clashApplicationInfoDTO.setEndDate(outOfOfficeEndDate);
                clashApplicationInfoDTO.setExpectedTime(RequestInfoHolder.isChinese() ? descCN : descEN);
                clashApplicationInfoDTOList.add(clashApplicationInfoDTO);
            }
        }
    }


    /**
     * mex特殊校验
     *
     * @param userId        用户id
     * @param errorCodeEnum 异常枚举
     */
    public void checkMexUser(Long userId,
                             ErrorCodeEnum errorCodeEnum,
                             List<String> employeeTypeList) {
        if (ObjectUtil.equal(mexQwbCheckFlag, "false")) {
            log.info("mexQwbCheckFlag is false");
            return;
        }
        if (ObjectUtil.isNull(userId)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR);
        }
        // 查询用户
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (ObjectUtil.isNull(userInfo)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }
        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(userInfo.getLocationCountry())
                && ObjectUtil.equal(userInfo.getIsWarehouseStaff(), BusinessConstant.Y)
                && employeeTypeList.contains(userInfo.getEmployeeType())) {
            // mex + 仓内 + 员工、挂靠 不允许销假
            throw BusinessLogicException.getException(errorCodeEnum);
        }
    }

    /**
     * 预览审批人员信息查询
     *
     * @param applyUserCode                     申请人
     * @param countryMap                        国家信息
     * @param hrUserCodeMap                     HR信息
     * @param userCodeMap                       用户信息
     * @param approvalUserInfoDTOS              所有审批人员
     * @param approvalCreateUserDTO             流程信息
     * @param approvalPreviewErrorUserDTOList   错误信息
     * @param approvalPreviewSuccessUserDTOList 正确信息
     */
    private void previewUserInfoFind(String applyUserCode,
                                     Map<String, List<CountryDTO>> countryMap,
                                     Map<String, List<AttendanceUser>> hrUserCodeMap,
                                     Map<String, List<AttendanceUser>> userCodeMap,
                                     List<ApprovalUserInfoDTO> approvalUserInfoDTOS,
                                     ApprovalCreateUserDTO approvalCreateUserDTO,
                                     List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList,
                                     List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList) {
        List<String> findUserCodeList = approvalUserInfoDTOS.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
        //所有指定人员
        List<ApprovalUserInfoDTO> appointUserList = approvalCreateUserDTO.getApprovalUserInfos();
        if (CollectionUtils.isEmpty(appointUserList)) {
            appointUserList = new ArrayList<>();
        }
        for (ApprovalUserInfoDTO userInfoDTO : appointUserList) {
            List<AttendanceUser> userList = userCodeMap.get(userInfoDTO.getUserCode());
            if (CollectionUtils.isEmpty(userList)) {
                continue;
            }
            //获取HR信息
            String hrUserCode = countryMainPocMap.get(userList.get(0).getOriginCountry());
            String userHrMessage = hrUserCode;
            if (StringUtils.isNotBlank(hrUserCode)) {
                List<AttendanceUser> hrUserDetailList = hrUserCodeMap.get(hrUserCode);
                if (CollectionUtils.isNotEmpty(hrUserDetailList)) {
                    userHrMessage = hrUserDetailList.get(0).getUserName();
                }
            }
            if (findUserCodeList.contains(userInfoDTO.getUserCode())) {
                //指定人员不存在
                if (StringUtils.equalsIgnoreCase(userList.get(0).getWorkStatus(), "DIMISSION")) {
                    ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工  " + userList.get(0).getUserName() + " 已经离职" : "The fixed approver " + userList.get(0).getUserName() + " has resigned");
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    errorUserDTO.setTemp(1);
                    approvalPreviewErrorUserDTOList.add(errorUserDTO);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(userList.get(0).getStatus(), "DISABLED")) {
                    ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工 " + userList.get(0).getUserName() + " 已经冻结" : "The fixed approver " + userList.get(0).getUserName() + " has been disabled");
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    errorUserDTO.setTemp(1);
                    approvalPreviewErrorUserDTOList.add(errorUserDTO);
                    continue;
                }
                //指定人员存在
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userList.get(0).getUserName() + " 是指定员工" : userList.get(0).getUserName() + " is fixed in process");
                successUserDTO.setUserHrMessage(userHrMessage);
                successUserDTO.setTemp(1);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                //continue;
            }
        }

        //所有角色对应人员
        List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = approvalCreateUserDTO.getApprovalCreateRoleUserDTOS();
        if (CollectionUtils.isEmpty(approvalCreateRoleUserDTOS)) {
            approvalCreateRoleUserDTOS = new ArrayList<>();
        }
        //申请人HR相关信息
        //获取HR信息
        List<AttendanceUser> applyUserList = userCodeMap.get(applyUserCode);
        if (CollectionUtils.isEmpty(applyUserList)) {
            return;
        }

        for (ApprovalCreateRoleUserDTO roleUserDTO : approvalCreateRoleUserDTOS) {
            List<ApprovalUserInfoDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(roleUserInfos)) {
                //这个角色没有找到人
                ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                errorUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                errorUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                errorUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                errorUserDTO.setTemp(2);
                approvalPreviewErrorUserDTOList.add(errorUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = applyUserList.get(0).getUserName() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    // String userMessageEn = applyUserInfoDetailApiDTOList.get(0).getUserName() + " - " + approvalRoleEnum.getDescUS() + " not found";
                    // 由于枚举是bpm定义的，改不了，所以直接先写死汇报上级的英文
                    String userMessageEn = applyUserList.get(0).getUserName() + " - " + "Line Manager" + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(applyUserList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {

                    List<AttendanceDept> deptApiDTOList = deptService.selectDeptByIds(
                            Collections.singletonList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptApiDTOList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptApiDTOList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameCn() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameEn() + " - " + approvalRoleEnum.getDescCN() + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(deptApiDTOList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                continue;
            }

            for (ApprovalUserInfoDTO userInfoDTO : roleUserInfos) {
                List<AttendanceUser> userList = userCodeMap.get(userInfoDTO.getUserCode());
                if (CollectionUtils.isEmpty(userList)) {
                    continue;
                }
                //这个角色找到人
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                successUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                successUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                successUserDTO.setUserCode(userList.get(0).getUserCode());
                successUserDTO.setTemp(2);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = userList.get(0).getUserName() + " is " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userList.get(0).getUserName() + " is " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(applyUserList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {
                    List<AttendanceDept> deptList = deptService.selectDeptByIds(Collections.singletonList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptList.get(0).getDeptNameCn() + approvalRoleEnum.getDescCN();
                    String userMessageEn = userList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptList.get(0).getDeptNameEn() + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(deptList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);

                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息

                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<AttendanceUser> hrUserList = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserList)) {
                            userHrMessage = hrUserList.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                }
            }
        }
    }

    /**
     * 构建预览实体
     */
    public void previewDTOBuildContainsErrors(List<ApprovalEmptyRecordApiDTO> recordApiDTOList,
                                              List<ApprovalDetailStepRecordDTO> resultDTOList,
                                              String userCode) {
        //获取所有国家的HR信息
        List<String> hrUserCodeList = new ArrayList<>();
        countryMainPocMap.forEach((k, v) -> hrUserCodeList.add(v));
        List<AttendanceUser> attendanceUsers = userService.listByUserCodes(hrUserCodeList);
        Map<String, List<AttendanceUser>> hrUserCodeMap = attendanceUsers.stream()
                .collect(Collectors.groupingBy(AttendanceUser::getUserCode));

        //获取所有国家信息
        List<CountryConfigDTO> countryConfigDTOList = countryService.queryAllCountryConfigList();
        List<CountryDTO> countryDTOList = BeanUtils.convert(CountryDTO.class, countryConfigDTOList);
        Map<String, List<CountryDTO>> countryMap = countryDTOList.stream()
                .collect(Collectors.groupingBy(CountryDTO::getCountryName));

        //获取所有人员(角色和指定人员总和)
        List<String> allAppointUserCodeList = new ArrayList<>();
        List<String> allRoleUserCodeList = new ArrayList<>();
        List<String> allUserCodeList = new ArrayList<>();
        for (ApprovalEmptyRecordApiDTO approvalEmptyRecordApiDTO : recordApiDTOList) {
            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = approvalEmptyRecordApiDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO == null) {
                approvalCreateUserApiDTO = new ApprovalCreateUserApiDTO();
            }
            //所有指定人员
            List<ApprovalUserInfoApiDTO> appointUserList = approvalCreateUserApiDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(appointUserList)) {
                appointUserList = new ArrayList<>();
            }
            List<String> appointUserCodeList = appointUserList.stream()
                    .map(ApprovalUserInfoApiDTO::getUserCode)
                    .collect(Collectors.toList());
            allAppointUserCodeList.addAll(appointUserCodeList);

            //所有角色对应人员
            List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserList =
                    approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserList)) {
                approvalCreateRoleUserList = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserApiDTO roleUserDTO : approvalCreateRoleUserList) {
                List<ApprovalUserInfoApiDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
                if (CollectionUtils.isEmpty(roleUserInfos)) {
                    continue;
                }
                List<String> roleUserCodeList = roleUserInfos.stream()
                        .map(ApprovalUserInfoApiDTO::getUserCode)
                        .collect(Collectors.toList());
                allRoleUserCodeList.addAll(roleUserCodeList);
            }
        }
        allUserCodeList.addAll(allAppointUserCodeList);
        allUserCodeList.addAll(allRoleUserCodeList);
        allUserCodeList.add(userCode);
        //调用HR接口，获取所有人员信息
        List<AttendanceUser> users = userService.listByUserCodes(allUserCodeList);
        Map<String, List<AttendanceUser>> userCodeMap = users.stream()
                .collect(Collectors.groupingBy(AttendanceUser::getUserCode));


        int temp = 1;
        for (ApprovalEmptyRecordApiDTO approvalEmptyRecordApiDTO : recordApiDTOList) {
            //发起人节点
            if (temp == 1) {
                ApprovalDetailStepRecordDTO createApprovalUserDTO = new ApprovalDetailStepRecordDTO();
                createApprovalUserDTO.setApprovalRecordType("approval");
                createApprovalUserDTO.setRecordStatus(-1);
                createApprovalUserDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setStepName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setRecordStatusUpdateDate(new Date());
                createApprovalUserDTO.setStepId("APPLY");
                List<ApprovalUserInfoDTO> approvalUserDTOList = AttendanceFormMapstruct.INSTANCE.mapApprovalUserInfoApiToDTO(approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
                createApprovalUserDTO.setApprovalUserInfoDTOS(approvalUserDTOList);
                resultDTOList.add(createApprovalUserDTO);
                temp++;
                continue;
            }
            //非发起人节点
            ApprovalDetailStepRecordDTO stepRecordDTO = AttendanceFormMapstruct.INSTANCE.mapApiDTOToStepRecordDTO(approvalEmptyRecordApiDTO);
            // 这边使用bpm返回的ApprovalRecordType，主要是为了区分抄送，前端可以把抄送过滤掉
            //stepRecordDTO.setApprovalRecordType("approval");
            stepRecordDTO.setRecordStatus(1);
            stepRecordDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "审批中" : "In review");
            //指定人员
            List<ApprovalUserInfoDTO> approvalUserDTOList =
                    AttendanceFormMapstruct.INSTANCE.mapApprovalUserInfoApiToDTO(approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
            approvalUserDTOList.forEach(item -> {
                item.setApprovalOperation("APPROVING");
            });
            stepRecordDTO.setApprovalUserInfoDTOS(approvalUserDTOList);

            //角色/指定人员处理
            //整体流程主节点信息
            List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList = new ArrayList<>();
            List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList = new ArrayList<>();
            //所有查询到的用户
            List<ApprovalUserInfoDTO> findUserList = stepRecordDTO.getApprovalUserInfoDTOS();

            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = approvalEmptyRecordApiDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO == null) {
                approvalCreateUserApiDTO = new ApprovalCreateUserApiDTO();
            }

            ApprovalCreateUserDTO approvalCreateUserDTO = new ApprovalCreateUserDTO();
            stepRecordDTO.setApprovalCreateUserDTO(approvalCreateUserDTO);
            List<ApprovalUserInfoApiDTO> approvalUserInfoApiDTOS = approvalCreateUserApiDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(approvalUserInfoApiDTOS)) {
                approvalUserInfoApiDTOS = new ArrayList<>();
            }
            approvalCreateUserDTO.setApprovalUserInfos(
                    AttendanceFormMapstruct.INSTANCE.mapApprovalUserInfoApiToDTO(approvalUserInfoApiDTOS));

            List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = new ArrayList<>();
            approvalCreateUserDTO.setApprovalCreateRoleUserDTOS(approvalCreateRoleUserDTOS);
            List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserApiDTOS =
                    approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserApiDTOS)) {
                approvalCreateRoleUserApiDTOS = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserApiDTO roleUserApiDTO : approvalCreateRoleUserApiDTOS) {
                ApprovalCreateRoleUserDTO roleUserDTO = new ApprovalCreateRoleUserDTO();
                roleUserDTO.setApprovalRole(roleUserApiDTO.getApprovalRole());
                roleUserDTO.setApprovalRoleName(roleUserApiDTO.getApprovalRoleName());
                roleUserDTO.setFetchObject(roleUserApiDTO.getFetchObject());
                roleUserDTO.setFetchObjectName(roleUserApiDTO.getFetchObjectName());
                roleUserDTO.setFetchObjectValue(roleUserApiDTO.getFetchObjectValue());
                roleUserDTO.setFetchObjectValueName(roleUserApiDTO.getFetchObjectValueName());
                roleUserDTO.setApprovalUserCodes(roleUserApiDTO.getApprovalUserCodes());
                if (CollectionUtils.isNotEmpty(roleUserApiDTO.getApprovalUserInfos())) {
                    roleUserDTO.setApprovalUserInfos(
                            AttendanceFormMapstruct.INSTANCE.mapApprovalUserInfoApiToDTO(roleUserApiDTO.getApprovalUserInfos()));
                }
                approvalCreateRoleUserDTOS.add(roleUserDTO);
            }

            previewUserInfoFind(userCode, countryMap, hrUserCodeMap, userCodeMap, findUserList, approvalCreateUserDTO, approvalPreviewErrorUserDTOList, approvalPreviewSuccessUserDTOList);

            approvalCreateUserDTO.setApprovalPreviewErrorUserDTOList(approvalPreviewErrorUserDTOList);
            approvalCreateUserDTO.setApprovalPreviewSuccessUserDTOList(approvalPreviewSuccessUserDTOList);
            resultDTOList.add(stepRecordDTO);
            temp++;
        }
    }

    /**
     * 构建参数实体
     *
     * @param leaveAddParam
     * @param outOfOfficeAddParam
     * @param reissueCardAddParam
     */
    public void userBaseInfoBuild(LeaveAddParam leaveAddParam,
                                  OutOfOfficeAddParam outOfOfficeAddParam,
                                  ReissueCardAddParam reissueCardAddParam) {
        Long userId = null;
        if (leaveAddParam != null) {
            userId = leaveAddParam.getUserId();
        }
        if (outOfOfficeAddParam != null) {
            userId = outOfOfficeAddParam.getUserId();
        }
        if (reissueCardAddParam != null) {
            userId = reissueCardAddParam.getUserId();
        }
        if (userId == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (userInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        if (Objects.nonNull(leaveAddParam)) {
            leaveAddParam.setUserCode(userInfo.getUserCode());
            leaveAddParam.setUserName(userInfo.getUserName());
            leaveAddParam.setDeptId(userInfo.getDeptId());
            leaveAddParam.setPostId(userInfo.getPostId());
            leaveAddParam.setCountry(userInfo.getLocationCountry());
            leaveAddParam.setOriginCountry(userInfo.getOriginCountry());
            leaveAddParam.setIsWarehouseStaff(userInfo.getIsWarehouseStaff());
        }
        if (Objects.nonNull(outOfOfficeAddParam)) {
            outOfOfficeAddParam.setUserCode(userInfo.getUserCode());
            outOfOfficeAddParam.setUserName(userInfo.getUserName());
            outOfOfficeAddParam.setDeptId(userInfo.getDeptId());
            outOfOfficeAddParam.setPostId(userInfo.getPostId());
            outOfOfficeAddParam.setCountry(userInfo.getLocationCountry());
            outOfOfficeAddParam.setOriginCountry(userInfo.getOriginCountry());
            outOfOfficeAddParam.setIsWarehouseStaff(userInfo.getIsWarehouseStaff());
        }
        if (Objects.nonNull(reissueCardAddParam)) {
            reissueCardAddParam.setUserCode(userInfo.getUserCode());
            reissueCardAddParam.setUserName(userInfo.getUserName());
            reissueCardAddParam.setDeptId(userInfo.getDeptId());
            reissueCardAddParam.setPostId(userInfo.getPostId());
            reissueCardAddParam.setCountry(userInfo.getLocationCountry());
            reissueCardAddParam.setOriginCountry(userInfo.getOriginCountry());
            reissueCardAddParam.setIsWarehouseStaff(userInfo.getIsWarehouseStaff());
        }
    }

    /**
     * 外勤/请假-每天时长计算明细
     * 需要获取请假开始时间前一天-请假结束时间后一天的所有排班
     */
    public void dayDurationInfoHandler(Long userId,
                                       Date startDate, Date endDate,
                                       CompanyLeaveConfigDO companyLeaveConfigDO,
                                       List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (userInfo == null || ObjectUtil.isEmpty(userInfo.getUserCode())) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }

        Long beforeDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(startDate, -1), "yyyyMMdd"));
        Long afterDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(endDate, 1), "yyyyMMdd"));
        // 获取打卡记录查询的开始时间和结束时间[这边查询的时间范围是前一天的开始时间和后一天的结束时间，存在跨天情况]
        DateTime punchRecordStartTime = DateUtil.beginOfDay(DateUtil.offsetDay(startDate, -1));
        DateTime punchRecordEndTime = DateUtil.endOfDay(DateUtil.offsetDay(endDate, 1));
        //查询用户的排班记录(前后各多加1天)  可以为空，没有排班
        List<UserShiftConfigDO> classEmployeeConfigList = userShiftConfigManage.selectRecordByUserIdList(Arrays.asList(userId), beforeDayId, afterDayId);
        Map<Long, List<UserShiftConfigDO>> employeeDayIdMap = classEmployeeConfigList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getDayId));

        //生成班次/明细  可能为OFF/PH排班 classId为空
        List<Long> punchClassIdList = classEmployeeConfigList
                .stream()
                .filter(item -> item.getPunchClassConfigId() != null)
                .map(item -> item.getPunchClassConfigId())
                .collect(Collectors.toList());
        List<PunchClassConfigDO> classConfigDOList = punchClassConfigManage.selectByClassIds(punchClassIdList);
        Map<Long, List<PunchClassConfigDO>> classConfig = classConfigDOList
                .stream()
                .collect(Collectors.groupingBy(PunchClassConfigDO::getId));

        // 获取打卡规则
        List<UserDayConfigDTO> punchConfigDOList = punchConfigManage.selectDayConfigByUserIdList(Arrays.asList(userId),
                punchRecordStartTime, punchRecordEndTime);
        Map<Long, List<UserDayConfigDTO>> punchConfigMap = punchConfigDOList
                .stream()
                .filter(item -> userId.equals(item.getUserId()))
                .collect(Collectors.groupingBy(UserDayConfigDTO::getDayId));
        // 获取班次详情
        List<PunchClassItemConfigDO> punchClassItemConfigDOList = punchClassConfigManage.selectClassItemByClassIds(punchClassIdList);
        Map<Long, List<PunchClassItemConfigDO>> punchClassItemConfigMap = punchClassItemConfigDOList
                .stream()
                .collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));

        // 查询用户信息
        // 查询用户的打卡记录
        EmployeePunchCardRecordQuery query = EmployeePunchCardRecordQuery
                .builder()
                .startTime(punchRecordStartTime)
                .endTime(punchRecordEndTime)
                .userCode(userInfo.getUserCode())
                .build();
        List<EmployeePunchRecordDO> allPunchRecordList = punchRecordService.listRecords(query);
        List<UserPunchRecordDTO> userPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            UserPunchRecordDTO userPunchRecordDTO = new UserPunchRecordDTO();
            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
            userPunchRecordDTO.setFormatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"));
            userPunchRecordDTOList.add(userPunchRecordDTO);
        }

        Long tempDayId = beforeDayId;
        while (tempDayId <= afterDayId) {
            Long finalTempDayId = tempDayId;
            //后移一天
            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
            //查询该天的排班
            List<UserShiftConfigDO> dayEmployeeList = employeeDayIdMap.get(finalTempDayId);

            //当天的结束时间
            Date finalDateNow = DateUtil.endOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"));
            //查询用户的打卡规则范围，看是否当天免打卡
            Boolean isNeedPunch = punchConfigManage.selectIfNeedPunchByUserIds(userId, finalDateNow);
            //当天免打卡
            if (isNeedPunch) {
                commonFormCalcService.dayDurationNoNeedHandler(beforeDayId, afterDayId, finalTempDayId,
                        startDate, endDate, dayDurationInfoDTOList);
                continue;
            }

            //找到循环当天得前一天的排班，需要验证是否跨天
            Long currentBeforeDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(finalTempDayId.toString()), -1), "yyyyMMdd"));
            List<UserShiftConfigDO> dayBeforeEmployeeList = employeeDayIdMap.get(currentBeforeDayId);
            List<PunchClassItemConfigDO> beforeDayItemList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(dayBeforeEmployeeList) && Objects.nonNull(dayBeforeEmployeeList.get(0).getPunchClassConfigId())) {
                beforeDayItemList = punchClassItemConfigMap.get(dayBeforeEmployeeList.get(0).getPunchClassConfigId());
            }
            //当天没有排班
            if (CollectionUtils.isEmpty(dayEmployeeList)) {
                commonFormCalcService.dayDurationNoShiftHandler(beforeDayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
                continue;
            }

            //当天有排班
            //当天是PH/OFF
            if (dayEmployeeList.get(0).getPunchClassConfigId() == null) {
                commonFormCalcService.dayDurationPhHandler(beforeDayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate
                        , dayEmployeeList.get(0).getDayShiftRule(), companyLeaveConfigDO, dayDurationInfoDTOList);
                continue;
            }

            List<UserDayConfigDTO> dayPunchConfigList = punchConfigMap.get(tempDayId);
            if (CollectionUtils.isEmpty(dayPunchConfigList)) {
                continue;
            }
            PunchConfigDO punchConfig = dayPunchConfigList.get(0).getPunchConfigDO();
            List<PunchClassConfigDO> dayPunchClassList = classConfig.get(dayEmployeeList.get(0).getPunchClassConfigId());
            if (CollectionUtils.isEmpty(dayPunchClassList)) {
                continue;
            }
            List<PunchClassItemConfigDO> dayItemList = punchClassItemConfigMap.get(dayEmployeeList.get(0).getPunchClassConfigId());
            if (CollectionUtils.isEmpty(dayItemList)) {
                continue;
            }
            dayItemList = dayItemList.stream().sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo)).collect(Collectors.toList());
            //当天排班是自由打卡规则
            if (StringUtils.equalsIgnoreCase(punchConfig.getConfigType(), PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode())) {
                commonFormCalcService.dayDurationFreeShiftHandler(dayPunchClassList.get(0), dayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
                continue;
            }
            //当天排班是一次打卡规则
            if (StringUtils.equalsIgnoreCase(punchConfig.getConfigType(), PunchConfigTypeEnum.FLEXIBLE_WORK_ONCE.getCode())) {
                commonFormCalcService.dayOnceNormalShiftHandler(dayPunchClassList.get(0), dayItemList, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
                continue;
            }
            //固定/班次
            commonFormCalcService.dayDurationNormalShiftHandler(dayPunchClassList.get(0), dayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList, userPunchRecordDTOList);
        }
    }

    /**
     * 判断用户处理的异常考勤是否正确(必须是审批中的异常)
     */
    public EmployeeAbnormalAttendanceDO userAbnormalRecordCheck(Long abnormal) {
        if (abnormal == null) {
            return null;
        }
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectByIdList(Arrays.asList(abnormal));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        //如果异常已经被别的单据使用了，审批中，报错提示
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
        // 如果异常已经处理或过期则提示
        if (AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(abnormalAttendanceDO.getStatus())) {
            throw BusinessException.get(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getDesc()));
        }
        if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
            throw BusinessException.get(ErrorCodeEnum.ABNORMAL_IN_REVIEW.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_IN_REVIEW.getDesc()));
        }
        return abnormalAttendanceDO;
    }

    /**
     * 构建用户请假记录数据
     *
     * @param param  入参
     * @param remark 备注
     * @param type   请假类型
     */
    public UserLeaveRecordDO buildUserLeaveRecord(BigDecimal totalLeaveTime,
                                                  LeaveAddParam param,
                                                  String type,
                                                  String remark) {
        UserLeaveRecordDO userLeaveRecord = new UserLeaveRecordDO();
        userLeaveRecord.setId(defaultIdWorker.nextId());
        userLeaveRecord.setUserId(param.getUserId());
        userLeaveRecord.setUserCode(param.getUserCode());
        userLeaveRecord.setDate(new Date());
        userLeaveRecord.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
        userLeaveRecord.setConfigId(param.getConfigId());
        userLeaveRecord.setLeaveName(param.getLeaveName());
        userLeaveRecord.setLeaveType(param.getLeaveType());
        userLeaveRecord.setType(type);
        userLeaveRecord.setLeaveStartDay(param.getLeaveStartDate());
        userLeaveRecord.setLeaveEndDay(param.getLeaveEndDate());
        userLeaveRecord.setLeaveMinutes(totalLeaveTime);
        userLeaveRecord.setRemark(remark);
        fillDOInsert(userLeaveRecord);
        userLeaveRecord.setOperationUserCode(RequestInfoHolder.getUserCode());
        userLeaveRecord.setOperationUserName(RequestInfoHolder.getUserName());
        return userLeaveRecord;
    }

    /**
     * 构建假期详情表数据
     *
     * @param userLeaveStageDetailInfoList 需要修改的假期详情数据
     * @param param                        入参
     * @param totalLeaveTime               请假总时长
     */
    public void buildUserLeaveStageDetailList(List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                                              LeaveAddParam param,
                                              BigDecimal totalLeaveTime) {
        UserLeaveDetailQuery query = UserLeaveDetailQuery.builder()
                .userId(param.getUserId())
                .configId(param.getConfigId())
                .build();
        List<UserLeaveDetailDO> userLeaveDetail = userLeaveDetailService.selectUserLeaveDetail(query);
        if (CollUtil.isEmpty(userLeaveDetail)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE);
        }
        List<Long> leaveIdList = userLeaveDetail.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = userLeaveStageDetailService.selectByLeaveId(leaveIdList);
        if (CollUtil.isEmpty(userLeaveStageDetailList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THE_USER_LACKS_DETAILED_DATA_FOR_THE_LEAVE_TYPE);
        }
        // 先扣除结转的假期，不够的时候再扣除非结转的假期余额,将假期先按照是否结转倒叙排序后再按照阶段排序
        // 将假期按照阶段倒序，优先使用比率高的假期余额
        List<UserLeaveStageDetailDO> reversedUserLeaveStageDetailList = userLeaveStageDetailList.stream()
                .sorted(Comparator.comparingInt(UserLeaveStageDetailDO::getLeaveMark)
                        .thenComparing(UserLeaveStageDetailDO::getPercentSalary).reversed())
                .collect(Collectors.toList());
        // 这边无需考虑假期余额不够扣减的情况，因为如果假期余额不够扣减，前面校验都不会通过的
        for (int i = 0; i < reversedUserLeaveStageDetailList.size(); i++) {
            UserLeaveStageDetailDO stageDetailInfo = reversedUserLeaveStageDetailList.get(i);
            // 不是最后一个阶梯，并且有假期<=0
            // 当前逻辑 ：1. 不是最后一个阶梯，判断假期余额是否小于等于0，如果小于等于0则跳过该假期阶梯。2. 如果是最后一个阶梯，直接使用最后一个阶梯
            //if (i != reversedUserLeaveStageDetailList.size() - 1 && stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) < 1) {
            //    continue;
            //}

            // 如果当前阶段假期余额 <= 0，则表示无法扣减当前阶段假期余额
            if (stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            /*
                当前阶段(请假时长 - 假期余额)
                    大于0:该阶段假期不足以请假，需要加上下一个阶段的假期余额
                    等于0:正好完成请该阶段的假期
                    小于0:可以完全请该阶段的假期
             */
            BigDecimal difference = totalLeaveTime.subtract(stageDetailInfo.getLeaveResidueMinutes());
            if (difference.compareTo(BigDecimal.ZERO) <= 0) {
                stageDetailInfo.setLeaveUsedMinutes(stageDetailInfo.getLeaveUsedMinutes().add(totalLeaveTime));
                stageDetailInfo.setLeaveResidueMinutes(stageDetailInfo.getLeaveResidueMinutes().subtract(totalLeaveTime));
                stageDetailInfo.setLastUpdDate(new Date());
                stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
                stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
                userLeaveStageDetailInfoList.add(stageDetailInfo);
                break;
            }
            // 提前存储假期剩余时间，为后面减法做准备
            BigDecimal subtractLeaveResidueMinutes = stageDetailInfo.getLeaveResidueMinutes();
            // 大于0：直接该阶段假期余额变成0，已使用 = 已使用 + 假期余额
            stageDetailInfo.setLeaveUsedMinutes(stageDetailInfo.getLeaveUsedMinutes().add(stageDetailInfo.getLeaveResidueMinutes()));
            stageDetailInfo.setLeaveResidueMinutes(BigDecimal.ZERO);
            stageDetailInfo.setLastUpdDate(new Date());
            stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
            userLeaveStageDetailInfoList.add(stageDetailInfo);
            // 更新请假时间，为后续循环准备
            totalLeaveTime = totalLeaveTime.subtract(subtractLeaveResidueMinutes);
        }
    }


    /**
     * 审批流完成处理假期相关记录
     *
     * @param formDetailBO                 假期申请单数据、假期申请单详情数据、考勤申请单关联表数据
     * @param userLeaveStageDetailInfoList 需要修改的用户假期余额数据列表
     * @param status                       请假：审批状态：-1拒绝、-2驳回、-3撤回
     * @return UserLeaveRecordDO
     */
    public UserLeaveRecordDO getUserLeaveStageDetailList(AttendanceFormDetailBO formDetailBO,
                                                         List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                                                         Integer status) {
        UserLeaveRecordDO userLeaveRecord = null;
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        if (ObjectUtil.isNull(formDO)) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        if (ObjectUtil.equal(formDO.getFormType(), FormTypeEnum.LEAVE.getCode()) &&
                ObjectUtil.notEqual(formDO.getFormStatus(), FormStatusEnum.REJECT.getCode())) {
            // 封装userLeaveStageDetailInfoList、userLeaveRecord
            String remark = "";
            switch (status) {
                case -1:
                    remark = "【clover审批】终止审核中请假申请单";
                    break;
                case -2:
                    remark = "【clover审批】驳回审核中请假申请单";
                    break;
                case -3:
                    remark = "【clover审批】撤回审核中请假申请单";
                    break;
                default:
                    break;
            }
            userLeaveRecord = this.handlerUserLeaveStageDetailList(formDetailBO, userLeaveStageDetailInfoList, remark);
        }
        return userLeaveRecord;
    }

    /**
     * 构建假期详情表数据
     *
     * @param detailBO                     假期申请单数据、假期申请单详情数据、考勤申请单关联表数据
     * @param userLeaveStageDetailInfoList 用来接收-需要修改的假期详情数据列表
     * @param remark                       用来接收-需要修改的假期详情数据列表
     * @return UserLeaveRecordDO
     */
    public UserLeaveRecordDO handlerUserLeaveStageDetailList(AttendanceFormDetailBO detailBO,
                                                             List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                                                             String remark) {
        // 假期申请单信息
        AttendanceFormDO formInfo = detailBO.getFormDO();
        // 假期申请单详情数据
        List<AttendanceFormAttrDO> attrInfo = detailBO.getAttrDOList();
        // 获取这笔销假申请所关联的原始申请单
        List<AttendanceFormRelationDO> relationInfo = detailBO.getRelationDOList();
        // 获取申请单详情数据-假期配置主键信息
        List<AttendanceFormAttrDO> configIdInfo = attrInfo.stream().
                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.configID.getLowerCode())).
                collect(Collectors.toList());
        // 获取申请单详情数据-请假名称信息
        List<AttendanceFormAttrDO> leaveNameInfo = attrInfo.stream().
                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode())).
                collect(Collectors.toList());
        // 获取申请单详情数据-请假开始时间
        List<AttendanceFormAttrDO> leaveStartDateInfo = attrInfo.stream().
                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).
                collect(Collectors.toList());
        // 获取申请单详情数据-请假结束时间
        List<AttendanceFormAttrDO> leaveEndDateInfo = attrInfo.stream().
                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).
                collect(Collectors.toList());
        // 如果请假名称集合为空，直接返回
        if (CollUtil.isEmpty(leaveNameInfo)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THERE_IS_NO_LEAVE_TYPE_INFORMATION_FOR_THE_DETAILS_OF_THE_APPLICATION_FORM);
        }
        // 如果请假开始时间、结束时间集合为空，直接返回
        if (CollUtil.isEmpty(leaveStartDateInfo) || CollUtil.isEmpty(leaveEndDateInfo)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THERE_IS_NO_LEAVE_START_OR_END_TIME_FOR_THE_DETAILS_OF_THE_APPLICATION_FORM);
        }
        // 转日期格式
        Date startDate = DateUtil.parse(leaveStartDateInfo.get(0).getAttrValue(), DatePattern.NORM_DATETIME_PATTERN);
        Date endDate = DateUtil.parse(leaveEndDateInfo.get(0).getAttrValue(), DatePattern.NORM_DATETIME_PATTERN);

        // 查询该假期配置
        // 修改为根据人员范围查询
        if (CollectionUtils.isEmpty(configIdInfo)
                || StringUtils.isBlank(configIdInfo.get(0).getAttrValue())) {
            log.info("FormCancel | formInfo :{}", formInfo.getApplicationCode());
            throw BusinessLogicException.getException(ErrorCodeEnum.APPLICATION_FORM_DOES_NOT_HAVE_VACATION);
        }
        CompanyLeaveConfigDO companyLeaveConfigDO = companyLeaveConfigService.getById(Long.valueOf(configIdInfo.get(0).getAttrValue()));

        // 重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        dayDurationInfoHandler(formInfo.getUserId(), startDate, endDate,
                companyLeaveConfigDO, dayDurationInfoDTOList);
        // 计算请假总时长-单位分钟
        BigDecimal totalLeaveTime = handlerLeaveTotalTime(dayDurationInfoDTOList);
        // 用户请假记录数据 需要总的请假时长，这边重新接收一下。保证不会变化
        BigDecimal userLeaveRecordTotalLeaveTime = BigDecimal.ZERO;

        UserLeaveDetailQuery query = new UserLeaveDetailQuery();
        // param参数在前面已经校验了
        query.setUserId(formInfo.getUserId());
        if (CollectionUtils.isNotEmpty(configIdInfo) && StringUtils.isNotBlank(configIdInfo.get(0).getAttrValue())) {
            query.setConfigId(Long.valueOf(configIdInfo.get(0).getAttrValue()));
        } else {
            query.setLeaveName(leaveNameInfo.get(0).getAttrValue());
        }
        List<UserLeaveDetailDO> userLeaveDetail = userLeaveDetailService.selectUserLeaveDetail(query);
        List<Long> leaveIdList = userLeaveDetail.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = userLeaveStageDetailService.selectByCondition(UserLeaveStageDetailQuery
                .builder()
                .leaveIdList(leaveIdList)
                .build());
        // 先返还非结转的假期，然后再返还结转的假期余额,将假期先按照是否结转排序后再按照阶段排序
        // 将假期按照阶段正序，优先返还比率低（其实就是请假扣钱多的）的假期余额
        List<UserLeaveStageDetailDO> orthodoxUserLeaveStageDetailList = userLeaveStageDetailList.stream()
                .filter(item -> WhetherEnum.NO.getKey().equals(item.getIsInvalid()))
                .sorted(Comparator.comparingInt(UserLeaveStageDetailDO::getLeaveMark)
                        .thenComparing(UserLeaveStageDetailDO::getPercentSalary))
                .collect(Collectors.toList());
        // 遍历正序的假期详情信息数据
        for (UserLeaveStageDetailDO stageDetailInfo : orthodoxUserLeaveStageDetailList) {
            // 不是最后一个阶梯，并且有假期<=0
            // 当前逻辑 ：1. 不是最后一个阶梯，判断假期余额是否小于等于0，如果小于等于0则跳过该假期阶梯。2. 如果是最后一个阶梯，直接使用最后一个阶梯
            //if (i != reversedUserLeaveStageDetailList.size() - 1 && stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) < 1) {
            //    continue;
            //}

            // 如果当前阶段所使用假期余额 <= 0，则表示无法扣减当前阶段已使用假期余额（也表示之前请假扣减假期余额、增加已使用假期余额，没有执行到该阶段假期）
            if (stageDetailInfo.getLeaveUsedMinutes().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            /*
                当前阶段(请假时长 - 已使用假期余额)
                    大于0:该阶段已使用假期不足以返还用户全部请假时间，需要加上下一个阶段的已使用假期余额
                    等于0:正好完全返还该阶段的请假时长
                    小于0:可以完全返还该阶段的请假时长
            */
            BigDecimal usedDifference = totalLeaveTime.subtract(stageDetailInfo.getLeaveUsedMinutes());
            if (usedDifference.compareTo(BigDecimal.ZERO) <= 0) {
                stageDetailInfo.setLeaveUsedMinutes(stageDetailInfo.getLeaveUsedMinutes().subtract(totalLeaveTime));
                stageDetailInfo.setLeaveResidueMinutes(stageDetailInfo.getLeaveResidueMinutes().add(totalLeaveTime));
                stageDetailInfo.setLastUpdDate(new Date());
                stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
                stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
                userLeaveStageDetailInfoList.add(stageDetailInfo);
                userLeaveRecordTotalLeaveTime = userLeaveRecordTotalLeaveTime.add(totalLeaveTime);
                break;
            }
            // 提前存储该阶段假期的已使用余额，为后续循环扣减使用
            BigDecimal subtractLeaveUsedMinutes = stageDetailInfo.getLeaveUsedMinutes();
            // 大于0：直接该阶段假期余额 = 假期余额 + 已使用假期余额，已使用假期余额变成0
            stageDetailInfo.setLeaveResidueMinutes(stageDetailInfo.getLeaveResidueMinutes().add(stageDetailInfo.getLeaveUsedMinutes()));
            stageDetailInfo.setLeaveUsedMinutes(BigDecimal.ZERO);
            stageDetailInfo.setLastUpdDate(new Date());
            stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
            userLeaveStageDetailInfoList.add(stageDetailInfo);
            // 更新请假时间，为后续循环准备
            totalLeaveTime = totalLeaveTime.subtract(subtractLeaveUsedMinutes);
            userLeaveRecordTotalLeaveTime = userLeaveRecordTotalLeaveTime.add(subtractLeaveUsedMinutes);
        }

        // 封装请假记录表
        LeaveAddParam leaveAddParam = new LeaveAddParam();
        leaveAddParam.setUserId(formInfo.getUserId());
        leaveAddParam.setUserCode(formInfo.getUserCode());
        if (CollectionUtils.isNotEmpty(configIdInfo) && StringUtils.isNotBlank(configIdInfo.get(0).getAttrValue())) {
            leaveAddParam.setConfigId(Long.valueOf(configIdInfo.get(0).getAttrValue()));
        }
        leaveAddParam.setLeaveType(companyLeaveConfigDO.getLeaveType());
        leaveAddParam.setLeaveName(leaveNameInfo.get(0).getAttrValue());
        leaveAddParam.setLeaveStartDate(startDate);
        leaveAddParam.setLeaveEndDate(endDate);

        //查询假期结转配置，如果请假开始时间小于结转时间，当前时间大于等于结转时间，则代表当前假期已经结转，收入记录备注存在结转假期
        //如果请假开始时间小于失效时间，当前时间大于等于失效时间，则代表当前结转假期已经失效，收入记录备注存在失效假期
        Integer carryOverStatus = companyLeaveConfigCarryOverService.checkIfCarryOver(companyLeaveConfigDO.getId()
                , formInfo.getCreateDate()
                , companyLeaveConfigDO.getCountry()
                , userLeaveStageDetailList);
        switch (carryOverStatus) {
            case 1:
                remark = "【假期已经结转，存在部分假期不返还】 The holiday has been carried forward, and some data may not be returned";
                break;
            case 2:
                remark = "【假期已经失效，存在部分假期不返还】 The holiday has expired, some data may not be returned";
                break;
            default:
                break;
        }

        return buildUserLeaveRecord(userLeaveRecordTotalLeaveTime, leaveAddParam, LeaveTypeEnum.CANCEL.getCode(), remark);
    }

    /**
     * 获取请假总时长-单位分钟
     *
     * @param dayDurationInfoList 每天请假时间
     */
    public BigDecimal handlerLeaveTotalTime(List<DayDurationInfoDTO> dayDurationInfoList) {
        BigDecimal totalMinutes = BigDecimal.ZERO;
        // 遍历每一天的请假时长
        for (DayDurationInfoDTO dayDurationInfo : dayDurationInfoList) {

            // 计算一天的请假时长-分钟（天 * 法定工作时长 8h * 一小时的分钟数）【这里面的法定时长为什么给8h：因为，假期给的时候一天是按照8h给，所以请一天假，需要扣假期8h】
            totalMinutes = totalMinutes.add(dayDurationInfo.getDays().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES));

            // 换算之后的小时数【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
            BigDecimal realHours = dayDurationInfo.getHours().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP);
            // 计算小时的请假时长-分钟（小时 * 一小时的分钟数）
            totalMinutes = totalMinutes.add(realHours.multiply(BusinessConstant.MINUTES));

            /*
                 计算分钟的请假时长-分钟【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
                    算法1：分钟/60min/工作时长h * 8h = 分钟转换成8h的分钟数
                    //BigDecimal realMinutesHours = dayDurationInfo.getMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP).multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
                    算法2：分钟 * 8h * 60min / 工作时长h * 60min
                    算法1 存在很大精度问题，因为有多次除法，每一次除法都会保留两位小数，然后四舍五入，所以会存在精度问题
                    算法2 只有一次除法，所以精度问题 小很多
            */

            BigDecimal realMinutes = dayDurationInfo.getMinutes().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES).divide(dayDurationInfo.getLegalWorkingHours().multiply(BusinessConstant.MINUTES), 2, RoundingMode.HALF_UP);

            totalMinutes = totalMinutes.add(realMinutes);
        }
        return totalMinutes;
    }


    /**
     * 权限校验
     *
     * @param param
     * @return
     */
    public UserAuthDTO userDeptAuthList(UserAuthParam param) {
        AttendanceUser applyUserInfo = userService.getByUserId(param.getUserId());
        if (applyUserInfo == null) {
            return null;
        }
        List<String> authLocationCountryList = attendancePermissionService.getUserLocationCountryPermission();
        List<Long> authDeptIdList = attendancePermissionService.getUserDeptPermission();

        if ((CollectionUtils.isEmpty(authLocationCountryList)
                && CollectionUtils.isEmpty(authDeptIdList))) {
            return null;
        }
        return UserAuthDTO.builder()
                .countryList(authLocationCountryList)
                .deptIds(authDeptIdList.stream().distinct().collect(Collectors.toList()))
                .build();
    }

    /**
     * 校验选中部门和用户部门权限
     *
     * @param query
     * @param userAuthDTO
     * @return
     */
    public Boolean checkDeptAuth(AttendanceApprovalInfoQuery query,
                                 UserAuthDTO userAuthDTO) {
        if (Objects.isNull(userAuthDTO)) {
            return Boolean.FALSE;
        }
        List<String> countryAuthList = userAuthDTO.getCountryList();
        List<Long> deptAuthList = userAuthDTO.getDeptIds();
        // 无常驻国权限也无部门权限
        if (CollectionUtils.isEmpty(countryAuthList)
                && CollectionUtils.isEmpty(deptAuthList)) {
            return Boolean.FALSE;
        }
        query.setAuthLocationCountryList(countryAuthList);
        query.setAuthDeptIdList(deptAuthList);
        return Boolean.TRUE;
    }

    /**
     * 构建通用表单实体
     *
     * @param formId
     * @param attrKey
     * @param attrValue
     * @return
     */
    public AttendanceFormAttrDO insertAttrDOBuild(Long formId, String attrKey, String attrValue) {
        AttendanceFormAttrDO formAttrDO = new AttendanceFormAttrDO();
        formAttrDO.setId(IdWorkerUtil.getId());
        formAttrDO.setFormId(formId);
        formAttrDO.setAttrKey(attrKey);
        formAttrDO.setAttrValue(attrValue);
        fillDOInsert(formAttrDO);
        return formAttrDO;
    }

    public void customFieldBuild(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                                 String fieldType, String fieldValue,
                                 Map<String, String> fieldValueMap) {
        ApprovalTypeFieldApiDTO fieldApiDTO = new ApprovalTypeFieldApiDTO();
        fieldApiDTO.setFieldType(fieldType);
        fieldApiDTO.setFieldValue(fieldValue);
        fieldApiDTO.setFieldValueMap(fieldValueMap);
        fieldApiDTOList.add(fieldApiDTO);
    }

    /**
     * 补卡申请  除了审批通过之外的一切操作，都要归还补卡次数
     */
    public UserCycleReissueCardCountDO reissueCardCountHandler(AttendanceFormDetailBO formDetailBO) {
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        List<AttendanceFormAttrDO> attrDOS = formDetailBO.getAttrDOList();
        if (!CollUtil.contains(FormTypeEnum.TYPE_OF_REISSUE_CARD_TYPE, formDO.getFormType())) {
            return null;
        }
        List<AttendanceFormAttrDO> reissueCardDayIdDO = attrDOS
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey()
                        , ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reissueCardDayIdDO)) {
            return null;
        }
        Long reissueCardDayId = Long.valueOf(reissueCardDayIdDO.get(0).getAttrValue());
        Date reissueCardDayDate = DateUtil.parse(reissueCardDayId.toString(), "yyyyMMdd");

        List<UserCycleReissueCardCountDO> userCardConfigDOS = userReissueCardCountService.selectByUserIdList(Arrays.asList(formDO.getUserId()));
        if (CollectionUtils.isEmpty(userCardConfigDOS)) {
            return null;
        }
        List<UserCycleReissueCardCountDO> userCycleReissueCardCountDOS = userCardConfigDOS.stream()
                .filter(item -> item.getCycleStartDate().compareTo(reissueCardDayDate) < 1 && item.getCycleEndDate().compareTo(reissueCardDayDate) > -1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userCycleReissueCardCountDOS)) {
            return null;
        }
        UserCycleReissueCardCountDO userCycleReissueCardCountDO = userCycleReissueCardCountDOS.get(0);
        userCycleReissueCardCountDO.setUsedReissueCardCount(userCycleReissueCardCountDO.getUsedReissueCardCount() - 1);
        BaseDOUtil.fillDOUpdate(userCycleReissueCardCountDO);
        return userCycleReissueCardCountDO;
    }

    /**
     * 审批单据参数构建
     *
     * @param param
     * @param formDO
     */
    public void commonFormAdd(BaseFormParam param,
                              AttendanceFormDO formDO,
                              String formType) {
        formDO.setApplyUserId(param.getApplyUserId());
        formDO.setUserId(param.getUserId());
        formDO.setUserCode(param.getUserCode());
        formDO.setUserName(param.getUserName());
        formDO.setDeptId(param.getDeptId());
        formDO.setPostId(param.getPostId());
        formDO.setCountry(param.getCountry());
        formDO.setOriginCountry(param.getOriginCountry());
        formDO.setIsWarehouseStaff(param.getIsWarehouseStaff());
        formDO.setFormType(formType);
    }

    /**
     * 审批api参数构建
     *
     * @param initInfoApiDTO
     * @param formDO
     */
    public void commonApprovalAdd(ApprovalInitInfoApiDTO initInfoApiDTO,
                                  AttendanceFormDO formDO) {
        initInfoApiDTO.setBizId(formDO.getId().toString());
        initInfoApiDTO.setApprovalType(formDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(formDO.getCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(formDO.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(formDO.getApplicationCode());
    }

    /**
     * 更新单据关联属性
     *
     * @param formDO
     * @param formDetailBO
     * @param initInfoApiDTO
     */
    public void updateFormBuild(AttendanceFormDO formDO,
                                AttendanceFormDetailBO formDetailBO,
                                ApprovalInitInfoApiDTO initInfoApiDTO,
                                ApprovalResultVO resultVO) {
        if (formDO.getApprovalId() != null && StringUtils.isNotBlank(formDO.getApprovalProcessInfo())) {
            ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO = JSON.parseObject(formDO.getApprovalProcessInfo(), ApprovalPushStatusMsgDTO.class);
            if (approvalPushStatusMsgDTO != null && approvalPushStatusMsgDTO.getStatus().equals(-2)) {
                initInfoApiDTO.setResubmitApprovalId(formDO.getApprovalId());
            }
        }
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        formDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        //需要删除所有旧的属性表/关联表数据
        this.formUpdate(formDO, formDetailBO, attendanceApprovalManage);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
    }

    /**
     * 审批单据更新
     *
     * @param formDetailBO
     * @param formDO
     * @param attendanceApprovalManage
     */
    public void formUpdate(AttendanceFormDO formDO,
                           AttendanceFormDetailBO formDetailBO,
                           AttendanceApprovalManage attendanceApprovalManage) {
        List<AttendanceFormAttrDO> oldAttrList = formDetailBO.getAttrDOList();
        oldAttrList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
            item.setLastUpdDate(new Date());
        });
        List<AttendanceFormRelationDO> oldRelationList = formDetailBO.getRelationDOList();
        oldRelationList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
        });
        attendanceApprovalManage.formUpdate(formDO, oldRelationList, oldAttrList);
    }

    /**
     * 关联异常
     *
     * @param formDO
     * @param formRelationDOList
     * @param employeeAbnormalOperationRecord
     * @param abnormalId
     */
    public void associateAbnormal(AttendanceFormDO formDO,
                                  List<AttendanceFormRelationDO> formRelationDOList,
                                  EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                                  Long abnormalId) {
        AttendanceFormRelationDO relationDO = new AttendanceFormRelationDO();
        relationDO.setId(IdWorkerUtil.getId());
        relationDO.setFormId(formDO.getId());
        relationDO.setRelationId(abnormalId);
        relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
        BaseDOUtil.fillDOInsert(relationDO);
        formRelationDOList.add(relationDO);
        employeeAbnormalOperationRecord.setId(IdWorkerUtil.getId());
        employeeAbnormalOperationRecord.setFormId(formDO.getId());
        employeeAbnormalOperationRecord.setAbnormalId(abnormalId);
    }

    /**
     * 撤销申请单据构建
     *
     * @param param
     * @param formDetailBO
     * @param formDO
     * @param formRelationDOS
     * @param formAttrDOList
     * @param applicationCode
     * @param formStatus
     */
    public void commonRevokeDataAddBuild(RevokeAddParam param,
                                         AttendanceFormDetailBO formDetailBO,
                                         AttendanceFormDO formDO,
                                         List<AttendanceFormRelationDO> formRelationDOS,
                                         List<AttendanceFormAttrDO> formAttrDOList,
                                         String applicationCode,
                                         String formType, String formStatus) {
        AttendanceFormDO leaveFormDO = formDetailBO.getFormDO();
        List<AttendanceFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOList();
        formDO.setId(IdWorkerUtil.getId());
        formDO.setApplyUserId(param.getApplyUserId());
        formDO.setUserId(leaveFormDO.getUserId());
        formDO.setUserCode(leaveFormDO.getUserCode());
        formDO.setUserName(leaveFormDO.getUserName());
        formDO.setDeptId(leaveFormDO.getDeptId());
        formDO.setPostId(leaveFormDO.getPostId());
        formDO.setCountry(leaveFormDO.getCountry());
        formDO.setOriginCountry(leaveFormDO.getOriginCountry());
        formDO.setIsWarehouseStaff(leaveFormDO.getIsWarehouseStaff());
        formDO.setFormStatus(formStatus);
        formDO.setApplicationCode(applicationCode);
        formDO.setFormType(formType);
        BaseDOUtil.fillDOInsert(formDO);

        for (AttendanceFormAttrDO leaveAttrDO : leaveAttrDOS) {
            AttendanceFormAttrDO revokeAttrDO = new AttendanceFormAttrDO();
            revokeAttrDO.setId(IdWorkerUtil.getId());
            revokeAttrDO.setFormId(formDO.getId());
            revokeAttrDO.setAttrKey(leaveAttrDO.getAttrKey());
            revokeAttrDO.setAttrValue(leaveAttrDO.getAttrValue());
            BaseDOUtil.fillDOInsert(revokeAttrDO);
            formAttrDOList.add(revokeAttrDO);
        }
        if (StringUtils.isNotBlank(param.getRevokeReason())) {
            formAttrDOList.add(this.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.revokeReason.getLowerCode(),
                    param.getRevokeReason()));
        }

        AttendanceFormRelationDO relationDO = new AttendanceFormRelationDO();
        relationDO.setId(IdWorkerUtil.getId());
        relationDO.setFormId(formDO.getId());
        relationDO.setRelationId(leaveFormDO.getId());
        relationDO.setRelationType(ApplicationRelationTypeEnum.APPLICATION_FORM.getCode());
        BaseDOUtil.fillDOInsert(relationDO);
        formRelationDOS.add(relationDO);
    }


}
